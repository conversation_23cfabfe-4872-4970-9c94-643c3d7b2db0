<view class="custom-tabbar fix flex-center fill-base b-1px-t data-v-852a8b4e"><block wx:for="{{configInfo.tabBar}}" wx:for-item="item" wx:for-index="index" wx:key="value"><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['changeTab',['$0'],[[['configInfo.tabBar','value',item.value,'value']]]]]]]}}" class="flex-center flex-column mt-sm data-v-852a8b4e" style="{{'width:'+(100/$root.g1+'%')+';'+('color:'+(cur==item.value?'#2e80fe':'#666')+';')}}" catchtap="__e"><u-icon vue-id="{{'2025dc66-1-'+index}}" name="{{item.icon}}" color="{{cur==item.value?'#599eff':'#c5cad4'}}" size="28" class="data-v-852a8b4e" bind:__l="__l"></u-icon><view class="text data-v-852a8b4e">{{item.name}}</view></view></block><block wx:else><view class="no-tabbar data-v-852a8b4e">暂无导航栏数据</view></block></block></view>