{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?1e83", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?8dbf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?14b1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?2da5", "uni-app:///pages/mine.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?9c62", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/mine.vue?34bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "args", "clearTimeout", "timeout", "components", "tabbar", "data", "isLoading", "inviteCode", "tmplIds", "code", "loginPopupVisible", "agreedToTerms", "bindPhonePopupVisible", "isBindingPhone", "bindPhoneSmsCountdown", "bindPhoneSmsTimer", "bindPhoneForm", "phone", "toolList", "icon", "text", "url", "activityItem", "orderList", "count", "computed", "storeUserInfo", "token", "er<PERSON><PERSON>", "isLoggedIn", "userInfo", "avatarUrl", "nick<PERSON><PERSON>", "userId", "pid", "console", "canBindPhone", "onLoad", "uni", "provider", "success", "fail", "onShow", "onPullDownRefresh", "Promise", "methods", "getmylogin", "gethuodongconfig", "getNowPosition", "type", "isHighAccuracy", "accuracy", "res1", "city_id", "position", "resolve", "getshifuinfo", "debounceGetHighlight", "getHighlight", "role", "item", "index", "handleContact", "getCurrentPlatform", "showLoginPopup", "platform", "app", "uniPlatform", "dingyue", "templateId", "templateCategoryId", "title", "content", "cancelText", "confirmText", "confirmColor", "withSubscriptions", "selectedTmplIds", "hideLoginPopup", "toggleAgreement", "showBindPhonePopup", "hideBindPhonePopup", "clearInterval", "validatePhone", "sendBindPhoneSmsCode", "response", "startBindPhoneCountdown", "navigateToAgreement", "handleBindPhone", "unionid", "registerID", "params", "shortCode", "registrationId", "updatedUserInfo", "key", "val", "initUserData", "navigateTo", "fetchUserInfo", "then", "createTime", "catch", "finally", "saveUserInfoToStorage", "handleInvalidSession", "onGetPhoneNumber", "mask", "e", "encryptedData", "iv", "loginWithWeixin", "isapp", "showToast", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,m2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC4Jv2B;AAGA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;EACA;EACA;IAAA;MAAAC;IAAA;IACA;IACAC;IACAC;MAAA;IAAA;EACA;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC,UACA,IACA,IACA,8CACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAR;MACA;MACAS;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAC;QACAH;QACAC;QACAC;MACA;MACAE;QACAJ;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA;IAEA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACAb;QACAc;QACAC;QACAC;QACAC;MACA;;MAEA;MACAC;MACAA;MACAA;QACAF;QACAhB;QACAe;QACAD;QACAG;MACA;MACAC;MAEA;IACA;IACAC;MACA,8DACA;IACA;EAAA,EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;MACAF;MACA;MACAG;IACA;IACA;IACA;MACAH;MACA;MACAG;IACA;MACA;MACA;QACAH;QACA;QACA;QACAG;MACA;IACA;IACA;;IAEAA;MACAC;MACAC;QACA;UACA;UACAL;QACA;MACA;MACAM;QACAN;MACA;IAEA;;IAEA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAO;IACAP;IACAA;IACAA;;IAEA;IACA;MACAA;MACA;;MAEA;MACA;QACAA;QACA;MACA;MAEA;IACA;MACAA;MACA;MACA;IACA;EACA;EACAQ;IACA;IACA;MACAC,aACA,sBACA,oBACA;QACAN;QACA;MACA;QACAH;QACAG;QACA;MACA;IACA;MACA;MACA;MACAA;MACA;IACA;EACA;EACAO;IACAC;MAAA;MACAR;QACAC;QACAC;UACA;YACA;YACAL;UACA;QACA;MACA;IACA;IACAY;MAAA;MACA;QACA;UACA;UACA;YAAA;UAAA;YACA;UACA;QACA;UACA;UACA;YAAA;UAAA;QACA;QACAZ;MACA;QACAA;QACA;QACA;UAAA;QAAA;MACA;IACA;IACAa;MAAA;MACA;QACAV;UACAW;UACAC;UACAC;UACAX;YACAF;YACAA;YACAA;cACAjB;cACAmB;gBACA;gBACA,kFACAY;gBACAd;kBACAe;kBACAC;gBACA;gBACAC;cACA;cACAd;gBACAN;gBACAoB;cACA;YACA;UACA;UACAd;YACAN;YACAoB;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACAlB;YACAjB;UACA;QACA;MACA;IACA;IACA;IACAoC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAvB;QACA;MACA;MACA;MACA;QACAF;QACA0B;MACA;QACAxB;QACA;QACA;UAAA,uCACAyB;YACApC,kEACAqC,6DACAA,uDACAA,qDACAA,yDACAA;UAAA;QAAA,CACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA3B;MACAA;IACA;EAAA,GACA;IACA;IACA4B;MAMA;MAOA;IACA;IAEAC;MACA;MACA7B;;MAEA;;MAaAA;MACA;;MAGA;MACA;MACAA;QACA8B;QACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACAjC;MACA;QACAA;QACA;QACA;UACAA;UACA;QACA;QACA;UAAA;QAAA;QACA;QACAA;QACA;UAAA;YACAkC;YACAC;UACA;QAAA;QACAhC;UACA9B;UACAgC;YACAL;YACA;YACA;cAAA;YAAA;YACA;YACA;cACAG;gBACAiC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAnC;kBACAF;kBACA;oBACAA;sBACAsC;oBACA;kBACA;oBACAtC;kBACA;gBACA;cACA;YACA;YACA;YACAuC;cACA1C;cACA;gBACA;gBACA;kBACA;oBACA;kBACA;gBACA;kBACA;gBACA;gBACAA;cACA;YACA;YACAA;UACA;UACAM;YACAN;UACA;QACA;MACA;IACA;IACA2C;MACA;MACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IACAC;MACA;MACA;QAAAhE;QAAAR;MAAA;MACA;QACAyE;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAnE;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA,iCACA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;kBAAAA;gBAAA;cAAA;gBAAAoE;gBAEA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmD;MAAA;MACA;MACA;QACA;QACA;UACAJ;UACA;QACA;MACA;IACA;IAEAK;MACA;MACA;QACAlE;MACA;QACAA;MACA;MACAiB;QACAjB;MACA;IACA;IAEA;IACAmE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,uBAEA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAAA,IAGA/E;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAGA;gBACA6B;kBAAAiC;gBAAA;gBAAA;gBAGA;gBACAkB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAC,+CACA;gBACAC;kBACA1E;kBACA2E;kBACAH;kBACAxB;kBAAA;kBACA4B;gBACA;;gBAEA1D;gBAAA;gBAAA,OAEA;cAAA;gBAAAkD;gBACAlD;gBAAA,MAEAkD;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACAS,kDACA;kBACA7E;gBAAA;gBAGA;kBACA8E;kBACAC;gBACA;;gBAEA;gBACA1D;;gBAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACA;cAAA;gBAAA;gBAEA;gBACAG;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2D;MACA9D;MACAA;MACAA;MAEA;QACAA;QAEA;UACAlB;UACAc;UACAC;UACAC;UACAC;UACAuD;QACA;QAEAtD;;QAEA;QACA;UACAA;UACA;YACA4D;YACAC;UACA;QACA;UACA7D;UACA;QACA;MACA;QACAA;MACA;QACAA;MACA;IACA;IACA+D;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACA;QAAA;MAAA;QACA;MACA;MACA5D;QACAjB;MACA;IACA;IACA8E;MAAA;MACA;QACAhE;QACA;MACA;MACA;MACA,iCACAiE;QACA;QACA;UACA;QACA;QACA;UACAnF;UACAc;UACAC;UACAC;UACAoE;UACAnE;UACA3B;QACA;QACA;UACAwF;UACAC;QACA;QACA;MACA,GACAM;QACAnE;QACA;UACA;QACA;UACA;YACA;UACA;QACA;MACA,GACAoE;QACA;MACA;IACA;IACAC;MACAlE;MACAA;MACAA;MACAA;MACAA;MACA;QACAA;MACA;IACA;IACAmE;MACA;QACAnE;MACA;MACA;QACAyD;QACAC;MACA;MACA;QACAD;QACAC;MACA;MACA;MACA;MACA;QAAA,uCACApC;UACApC;QAAA;MAAA,CACA;IACA;IACAkF;MAAA;MACAvE;MACA;QACA;MACA;MACA;MACA;MACAG;QACAqE;QACApC;MACA;MACA,gBAGAqC;QAFAC;QACAC;MAEAxE;QACAE;UACA;YACA/B;YACAoG;YACAC;YACA7C;YACA/B;UACA;QACA;QACAO;UACAH;YACAC;YACAC;cACA;gBACA;gBACAL;gBACA;kBACA1B;kBACAoG;kBACAC;kBACA7C;kBACA/B;gBACA;cACA;gBACA;gBACAI;gBACA;cACA;YACA;YACAG;cACA;cACAH;cACA;YACA;UACA;QACA;MACA;IACA;IACAyE;MAAA;MACA;MACA;MACA5E;MAEA;QACA1B;QACAoG;QACAC;QACA7C;QACA/B;QACA8E;MACA,GACAZ;QACAjE;QACA;UACA;QACA;QACAG;QACA;UACAyD;UACAC;QACA;QACA;MACA,GACAI;QACA;QACA;UACA;QACA;QACA;UACAnF;UACAc;UACAC;UACAC;UACAoE;UACAnE;QACA;QACA;UACA6D;UACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACAM;QACAnE;QACA;QACA;MACA,GACAoE;QACA;QACAjE;MACA;IACA;IACA2E;MAAA;MACA3E;QACAiC;QACApD;QACA+F;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACxgCA;AAAA;AAAA;AAAA;AAAkkD,CAAgB,shDAAG,EAAC,C;;;;;;;;;;;ACAtlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=ef6e6e68&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=template&id=ef6e6e68&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"pages-mine\">\n\t\t<!-- Header Section -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"avatar_view\">\n\t\t\t\t\t<image mode=\"aspectFill\" class=\"avatar\" :src=\"userInfo.avatarUrl\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t<view v-if=\"!isLoggedIn\">\n\t\t\t\t\t\t<button @click=\"showLoginPopup\" :disabled=\"isLoading\" :class=\"{ 'loading': isLoading }\">\n\t\t\t\t\t\t\t{{ isLoading ? '登录中...' : '用户登录' }}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"user-info-logged\">\n\t\t\t\t\t\t<view class=\"nickname\">{{ userInfo.nickName }}</view>\n\t\t\t\t\t\t<view v-if=\"userInfo.phone\" class=\"phone-number\">{{ userInfo.phone }}</view>\n\t\t\t\t\t\t<view v-else class=\"bind-phone-container\">\n\t\t\t\t\t\t\t<button @click=\"showBindPhonePopup\" class=\"bind-phone-btn\" :disabled=\"isBindingPhone\">\n\t\t\t\t\t\t\t\t{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view @click=\"navigateTo('../user/userProfile')\" class=\"settings\">\n\t\t\t\t\t<i class=\"iconfont icon-xitong text-bold\"></i>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Action Buttons Section (My Orders) -->\n\t\t<view class=\"mine-menu-list box-shadow fill-base box1\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">我的订单</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t\t<view @click=\"dingyue()\" class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList\" :key=\"index\" @tap=\"navigateTo(item.url)\">\n\t\t\t\t\t<view class=\"icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t<view v-if=\"item.count > 0\" class=\"number-circle\">{{ item.count }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Spacer -->\n\t\t<view class=\"spacer\"></view>\n\n\t\t<!-- Menu List Section -->\n\t\t<view class=\"mine-tool-list fill-base\">\n\t\t\t<view class=\"flex-warp\">\n\t\t\t\t<view class=\"list-item\" :class=\"{ 'master-side': item.text === '切换师傅版' || item.text === '邀请有礼' }\"\n\t\t\t\t\tv-for=\"(item, index) in toolList\" :key=\"index\" @tap=\"navigateTo(item.url)\">\n\t\t\t\t\t<u-icon :name=\"item.icon\"\n\t\t\t\t\t\t:color=\"item.text === '切换师傅版' || item.text === '邀请有礼' ? '#E41F19' : '#599eff'\" size=\"24\"></u-icon>\n\t\t\t\t\t<view class=\"item-text\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Floating Contact Button -->\n\t\t<view class=\"floating-contact\">\n\t\t\t<view class=\"contact-container\">\n\t\t\t\t<u-icon name=\"server-man\" color=\"#576b95\" size=\"24\"></u-icon>\n\t\t\t\t<button class=\"contact-btn\" open-type=\"contact\" bindcontact=\"handleContact\" session-from=\"sessionFrom\">\n\t\t\t\t\t客服\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Login Popup -->\n\t\t<view v-if=\"loginPopupVisible\" class=\"login-popup-overlay\" @tap=\"hideLoginPopup\">\n\t\t\t<view class=\"login-popup\" @tap.stop>\n\t\t\t\t<!-- Close Button -->\n\t\t\t\t<view class=\"close-btn\" @tap=\"hideLoginPopup\">\n\t\t\t\t\t<i class=\"iconfont icon-close\"></i>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- Popup Content -->\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<view class=\"welcome-title\">欢迎登录今师傅</view>\n\t\t\t\t\t<view class=\"welcome-subtitle\">登录后即可享受完整服务</view>\n\n\t\t\t\t\t<!-- Agreement Checkbox -->\n\t\t\t\t\t<view class=\"agreement-section\">\n\t\t\t\t\t\t<view class=\"checkbox-container\" @tap=\"toggleAgreement\">\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ 'checked': agreedToTerms }\">\n\t\t\t\t\t\t\t\t<i v-if=\"agreedToTerms\" class=\"iconfont icon-check\">✓</i>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t\t\t我已阅读并同意 <text class=\"link\" @tap.stop=\"navigateToAgreement('service')\">《今师傅服务协议》</text>\n\t\t\t\t\t\t\t\t<text class=\"link\" @tap.stop=\"navigateToAgreement('privacy')\">《隐私政策》</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- Login Button -->\n\t\t\t\t\t<button class=\"phone-login-btn\" :class=\"{ 'disabled': !agreedToTerms || isLoading }\"\n\t\t\t\t\t\t:disabled=\"!agreedToTerms || isLoading\" open-type=\"getPhoneNumber\"\n\t\t\t\t\t\t@getphonenumber=\"onGetPhoneNumber\">\n\t\t\t\t\t\t{{ isLoading ? '登录中...' : '手机号快捷登录' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Bind Phone Popup -->\n\t\t<view v-if=\"bindPhonePopupVisible\" class=\"login-popup-overlay\" @tap=\"hideBindPhonePopup\">\n\t\t\t<view class=\"login-popup\" @tap.stop>\n\t\t\t\t<!-- Close Button -->\n\t\t\t\t<view class=\"close-btn\" @tap=\"hideBindPhonePopup\">\n\t\t\t\t\t<i class=\"iconfont icon-close\"></i>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- Popup Content -->\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<view class=\"welcome-title\">绑定手机号</view>\n\t\t\t\t\t<view class=\"welcome-subtitle\">绑定手机号后可享受完整服务</view>\n\n\t\t\t\t\t<!-- Phone Input -->\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"phone\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入手机号\" v-model=\"bindPhoneForm.phone\" maxlength=\"11\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"chat\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入验证码\" v-model=\"bindPhoneForm.code\" maxlength=\"6\" />\n\t\t\t\t\t\t\t<view class=\"sms-btn\" @click=\"sendBindPhoneSmsCode\" :class=\"{ disabled: bindPhoneSmsCountdown > 0 }\">\n\t\t\t\t\t\t\t\t{{ bindPhoneSmsCountdown > 0 ? `${bindPhoneSmsCountdown}s` : '获取验证码' }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- Bind Button -->\n\t\t\t\t\t<button class=\"phone-login-btn\" :class=\"{ 'disabled': !canBindPhone || isBindingPhone }\"\n\t\t\t\t\t\t:disabled=\"!canBindPhone || isBindingPhone\" @click=\"handleBindPhone\">\n\t\t\t\t\t\t{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Tabbar -->\n\t\t<tabbar cur=\"3\"></tabbar>\n\t</view>\n</template>\n\n<script>\n\timport tabbar from \"@/components/tabbar.vue\";\n\timport {\n\t\tmapState,\n\t\tmapMutations\n\t} from 'vuex';\n\n\t// Utility function for debouncing\n\tconst debounce = (func, wait) => {\n\t\tlet timeout;\n\t\treturn function(...args) {\n\t\t\tconst context = this;\n\t\t\tclearTimeout(timeout);\n\t\t\ttimeout = setTimeout(() => func.apply(context, args), wait);\n\t\t};\n\t};\n\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisLoading: false,\n\t\t\t\tinviteCode: '',\n\t\t\t\ttmplIds: [\n\t\t\t\t\t'',\n\t\t\t\t\t'',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tcode: '', // Store wx.login code\n\t\t\t\tloginPopupVisible: false, // Control login popup visibility\n\t\t\t\tagreedToTerms: false, // Control agreement checkbox state\n\t\t\t\t// 绑定手机号相关\n\t\t\t\tbindPhonePopupVisible: false,\n\t\t\t\tisBindingPhone: false,\n\t\t\t\tbindPhoneSmsCountdown: 0,\n\t\t\t\tbindPhoneSmsTimer: null,\n\t\t\t\tbindPhoneForm: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tcode: ''\n\t\t\t\t},\n\t\t\t\ttoolList: [{\n\t\t\t\t\t\ticon: 'coupon',\n\t\t\t\t\t\ttext: '优惠券',\n\t\t\t\t\t\turl: '../user/coupon'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'map',\n\t\t\t\t\t\ttext: '我的地址',\n\t\t\t\t\t\turl: '../user/address'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'level',\n\t\t\t\t\t\ttext: '师傅入驻',\n\t\t\t\t\t\turl: '../shifu/Settle'\n\t\t\t\t\t},\n\t\t\t\t\t// {\n\t\t\t\t\t// \ticon: 'man-add',\n\t\t\t\t\t// \ttext: '切换师傅版',\n\t\t\t\t\t// \turl: '../shifu/Receiving'\n\t\t\t\t\t// },\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'red-packet-fill',\n\t\t\t\t\t\ttext: '邀请有礼',\n\t\t\t\t\t\turl: '../user/promotion'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'man-add',\n\t\t\t\t\t\ttext: '代理商申请',\n\t\t\t\t\t\turl: '../user/agent_apply'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'man-add',\n\t\t\t\t\t\ttext: '提现管理',\n\t\t\t\t\t\turl: '../user/coreWallet'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tactivityItem: {\n\t\t\t\t\ticon: 'heart-fill',\n\t\t\t\t\ttext: '限时活动',\n\t\t\t\t\turl: '../user/huodong_index'\n\t\t\t\t},\n\t\t\t\torderList: [{\n\t\t\t\t\t\ticon: 'order',\n\t\t\t\t\t\ttext: '全部订单',\n\t\t\t\t\t\turl: '../user/order_list?tab=0',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'file-text',\n\t\t\t\t\t\ttext: '报价列表',\n\t\t\t\t\t\turl: '../user/order_list?tab=-2',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'hourglass-half-fill',\n\t\t\t\t\t\ttext: '待支付',\n\t\t\t\t\t\turl: '../user/order_list?tab=1',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'bell',\n\t\t\t\t\t\ttext: '待服务',\n\t\t\t\t\t\turl: '../user/order_list?tab=5',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'clock',\n\t\t\t\t\t\ttext: '服务中',\n\t\t\t\t\t\turl: '../user/order_list?tab=6',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'thumb-up',\n\t\t\t\t\t\ttext: '已完成',\n\t\t\t\t\t\turl: '../user/order_list?tab=7',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\tstoreUserInfo: state => state.user.userInfo || {},\n\t\t\t\ttoken: state => state.user.autograph || '',\n\t\t\t\terweima: state => state.user.erweima || ''\n\t\t\t}),\n\t\t\tisLoggedIn() {\n\t\t\t\t// 如果有token，就认为已登录，即使用户信息还在加载中\n\t\t\t\treturn !!this.token;\n\t\t\t},\n\t\t\tuserInfo() {\n\t\t\t\t// 优先从Vuex获取，如果没有则从本地存储获取\n\t\t\t\tconst vuexUserInfo = this.storeUserInfo;\n\t\t\t\tconst localUserId = uni.getStorageSync('userId');\n\t\t\t\tconst localPhone = uni.getStorageSync('phone');\n\t\t\t\tconst localNickName = uni.getStorageSync('nickName');\n\t\t\t\tconst localAvatarUrl = uni.getStorageSync('avatarUrl');\n\t\t\t\tconst localPid = uni.getStorageSync('pid');\n\n\t\t\t\tconst result = {\n\t\t\t\t\tphone: vuexUserInfo.phone || localPhone || '',\n\t\t\t\t\tavatarUrl: vuexUserInfo.avatarUrl || localAvatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: vuexUserInfo.nickName || localNickName || '微信用户',\n\t\t\t\t\tuserId: vuexUserInfo.userId || localUserId || '',\n\t\t\t\t\tpid: vuexUserInfo.pid || localPid || ''\n\t\t\t\t};\n\n\t\t\t\t// 添加调试信息\n\t\t\t\tconsole.log('userInfo computed 被调用');\n\t\t\t\tconsole.log('vuexUserInfo:', vuexUserInfo);\n\t\t\t\tconsole.log('本地存储数据:', {\n\t\t\t\t\tuserId: localUserId,\n\t\t\t\t\tphone: localPhone,\n\t\t\t\t\tnickName: localNickName,\n\t\t\t\t\tavatarUrl: localAvatarUrl,\n\t\t\t\t\tpid: localPid\n\t\t\t\t});\n\t\t\t\tconsole.log('最终userInfo结果:', result);\n\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\tcanBindPhone() {\n\t\t\t\treturn this.bindPhoneForm.phone && this.bindPhoneForm.code &&\n\t\t\t\t\t   this.validatePhone(this.bindPhoneForm.phone);\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// Get current location\n\t\t\tthis.getNowPosition();\n\t\t\t// Handle invite code from options\n\t\t\tif (options.inviteCode) {\n\t\t\t\tconsole.log('Received inviteCode:', options.inviteCode);\n\t\t\t\tthis.inviteCode = options.inviteCode;\n\t\t\t\tuni.setStorageSync('receivedInviteCode', options.inviteCode);\n\t\t\t}\n\t\t\t// Handle erweima from Vuex or storage\n\t\t\tif (this.erweima) {\n\t\t\t\tconsole.log('erweima from Vuex:', this.erweima);\n\t\t\t\tthis.inviteCode = this.erweima;\n\t\t\t\tuni.setStorageSync('receivedInviteCode', this.erweima);\n\t\t\t} else {\n\t\t\t\tconst erweima = uni.getStorageSync('erweima');\n\t\t\t\tif (erweima) {\n\t\t\t\t\tconsole.log('erweima from storage:', erweima);\n\t\t\t\t\tthis.$store.commit('setErweima', erweima);\n\t\t\t\t\tthis.inviteCode = erweima;\n\t\t\t\t\tuni.setStorageSync('receivedInviteCode', erweima);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// Perform WeChat login\n\t\t\t // #ifdef MP-WEIXIN\n\t\t\tuni.login({\n\t\t\t\tprovider: 'weixin',\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\tconsole.log('Initial wx.login code:', this.code);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: err => {\n\t\t\t\t\tconsole.error('wx.login failed:', err);\n\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t});\n\t\t\t// #endif\n\t\t\t// Fetch activity config\n\t\t\t// this.gethuodongconfig();\n\t\t\t// Initialize user data\n\t\t\tthis.initUserData();\n\t\t\t// Fetch highlight if logged in\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tthis.debounceGetHighlight();\n\t\t\t}\n\t\t},\n\t\tonShow() {\n\t\t\tconsole.log('mine页面onShow，检查登录状态');\n\t\t\tconsole.log('token:', this.token);\n\t\t\tconsole.log('storeUserInfo:', this.storeUserInfo);\n\n\t\t\t// 如果有token，初始化用户数据\n\t\t\tif (this.token) {\n\t\t\t\tconsole.log('有token，初始化用户数据');\n\t\t\t\tthis.initUserData();\n\n\t\t\t\t// 如果Vuex中没有用户信息，尝试获取\n\t\t\t\tif (!this.storeUserInfo.userId) {\n\t\t\t\t\tconsole.log('Vuex中没有用户信息，尝试获取');\n\t\t\t\t\tthis.fetchUserInfo();\n\t\t\t\t}\n\n\t\t\t\tthis.debounceGetHighlight();\n\t\t\t} else {\n\t\t\t\tconsole.log('没有token，处理未登录状态');\n\t\t\t\t// Ensure UI reflects logged-out state\n\t\t\t\tthis.handleInvalidSession();\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\t// Handle pull-down refresh\n\t\t\tif (this.isLoggedIn && this.token) {\n\t\t\t\tPromise.all([\n\t\t\t\t\tthis.fetchUserInfo(),\n\t\t\t\t\tthis.getHighlight()\n\t\t\t\t]).then(() => {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t// this.showToast('刷新成功', 'success');\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('Pull-down refresh failed:', err);\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t// this.showToast('刷新失败，请稍后重试');\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// If not logged in, reset UI and stop refresh\n\t\t\t\tthis.handleInvalidSession();\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\tthis.showToast('请先登录');\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgetmylogin() {\n\t\t\t\tuni.login({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\t\tconsole.log('Initial wx.login code:', this.code);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgethuodongconfig() {\n\t\t\t\tthis.$api.service.huodongselectActivityConfig().then(res => {\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t// Add activity item if not already present\n\t\t\t\t\t\tif (!this.toolList.some(item => item.text === this.activityItem.text)) {\n\t\t\t\t\t\t\tthis.toolList = [...this.toolList, this.activityItem];\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Remove activity item if present\n\t\t\t\t\t\tthis.toolList = this.toolList.filter(item => item.text !== this.activityItem.text);\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log('huodongselectActivityConfig response:', res);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('huodongselectActivityConfig failed:', err);\n\t\t\t\t\t// Ensure activity item is removed on error\n\t\t\t\t\tthis.toolList = this.toolList.filter(item => item.text !== this.activityItem.text);\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetNowPosition() {\n\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\tuni.getLocation({\n\t\t\t\t\t\ttype: \"gcj02\",\n\t\t\t\t\t\tisHighAccuracy: true,\n\t\t\t\t\t\taccuracy: \"best\",\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tuni.setStorageSync(\"lat\", res.latitude);\n\t\t\t\t\t\t\tuni.setStorageSync(\"lng\", res.longitude);\n\t\t\t\t\t\t\tuni.request({\n\t\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,\n\t\t\t\t\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t\t\t\t\tconst province = res1.data.regeocode.addressComponent.province;\n\t\t\t\t\t\t\t\t\tthis.position = typeof res1.data.regeocode.addressComponent.city === \"string\" ?\n\t\t\t\t\t\t\t\t\t\tres1.data.regeocode.addressComponent.city : province;\n\t\t\t\t\t\t\t\t\tuni.setStorageSync(\"city\", {\n\t\t\t\t\t\t\t\t\t\tcity_id: res1.data.regeocode.addressComponent.adcode,\n\t\t\t\t\t\t\t\t\t\tposition: this.position\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tconsole.error(\"逆地理编码失败:\", err);\n\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error(\"获取定位失败:\", err);\n\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetshifuinfo() {\n\t\t\t\tthis.$api.shifu.checkMaster().then(res => {\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: '../shifu/mine'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// Debounced getHighlight to prevent multiple rapid calls\n\t\t\tdebounceGetHighlight: debounce(function() {\n\t\t\t\tthis.getHighlight();\n\t\t\t}, 300),\n\t\t\tgetHighlight() {\n\t\t\t\tconst userId = uni.getStorageSync('userId');\n\t\t\t\tif (!userId) {\n\t\t\t\t\tconsole.log('No userId, skipping getHighlight');\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t}\n\t\t\t\tthis.isLoading = true;\n\t\t\t\treturn this.$api.service.getHighlight({\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\trole: 1\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log('getHighlight response:', res);\n\t\t\t\t\t// Create a new array to ensure reactivity\n\t\t\t\t\tconst updatedOrderList = this.orderList.map((item, index) => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tcount: index === 0 ? (res && res.countOrder ? res.countOrder : 0) :\n\t\t\t\t\t\t\tindex === 1 ? (res && res.shiFuBaoJia ? res.shiFuBaoJia : 0) :\n\t\t\t\t\t\t\tindex === 2 ? (res && res.daiZhiFu ? res.daiZhiFu : 0) :\n\t\t\t\t\t\t\tindex === 3 ? (res && res.daiFuWu ? res.daiFuWu : 0) :\n\t\t\t\t\t\t\tindex === 4 ? (res && res.fuWuZhong ? res.fuWuZhong : 0) :\n\t\t\t\t\t\t\tindex === 5 ? (res && res.yiWanCheng ? res.yiWanCheng : 0) : 0\n\t\t\t\t\t}));\n\t\t\t\t\t// Update orderList reactively\n\t\t\t\t\tthis.$set(this, 'orderList', updatedOrderList);\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t});\n\t\t\t},\n\t\t\thandleContact(e) {\n\t\t\t\tconsole.log(e.detail.path);\n\t\t\t\tconsole.log(e.detail.query);\n\t\t\t},\n\t\t\t...mapMutations(['updateUserItem']),\n\t\t\t// 获取当前平台类型\n\t\t\tgetCurrentPlatform() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\treturn 'app-plus';\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\treturn 'mp-weixin';\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn 'h5';\n\t\t\t\t// #endif\n\n\t\t\t\treturn 'unknown';\n\t\t\t},\n\n\t\t\tshowLoginPopup() {\n\t\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\t\tconsole.log('当前平台:', platform);\n\n\t\t\t\t// 检查当前运行环境\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tconsole.log('APP端跳转到登录页面，isapp: 1');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login',\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转登录页面失败:', err);\n\t\t\t\t\t\tthis.showToast('跳转失败，请重试');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\tconsole.log('非APP端显示登录弹窗，isapp: 0');\n\t\t\t\tthis.loginPopupVisible = true;\n\t\t\t\t// #endif\n\n\t\t\t\t// 运行时检查作为备用方案\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\tconsole.log('系统信息:', {\n\t\t\t\t\tplatform: systemInfo.platform,\n\t\t\t\t\tapp: systemInfo.app,\n\t\t\t\t\tuniPlatform: platform\n\t\t\t\t});\n\t\t\t},\n\t\t\tdingyue() {\n\t\t\t\tconst panduan = uni.getStorageSync('userId');\n\t\t\t\tconsole.log(panduan);\n\t\t\t\tif (panduan) {\n\t\t\t\t\tconsole.log('dingyue called');\n\t\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tconst shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());\n\t\t\t\t\tconst selectedTmplIds = shuffled.slice(0, 3);\n\t\t\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\t\ttemplateId: id,\n\t\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t\t}));\n\t\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t\t// Check if any of the template IDs were rejected\n\t\t\t\t\t\t\tconst hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');\n\t\t\t\t\t\t\tconst hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');\n\t\t\t\t\t\t\tif (hasRejection && !hasShownModal) {\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\tconfirmText: '去开启',\n\t\t\t\t\t\t\t\t\tconfirmColor: '#007AFF',\n\t\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasShownSubscriptionModal', true);\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\t\twithSubscriptions: true\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t} else if (modalRes.cancel) {\n\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasCanceledSubscription', true);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thideLoginPopup() {\n\t\t\t\tthis.loginPopupVisible = false;\n\t\t\t\tthis.agreedToTerms = false;\n\t\t\t},\n\t\t\ttoggleAgreement() {\n\t\t\t\tthis.agreedToTerms = !this.agreedToTerms;\n\t\t\t},\n\n\t\t\t// 绑定手机号相关方法\n\t\t\tshowBindPhonePopup() {\n\t\t\t\tthis.bindPhonePopupVisible = true;\n\t\t\t},\n\t\t\thideBindPhonePopup() {\n\t\t\t\tthis.bindPhonePopupVisible = false;\n\t\t\t\tthis.bindPhoneForm = { phone: '', code: '' };\n\t\t\t\tif (this.bindPhoneSmsTimer) {\n\t\t\t\t\tclearInterval(this.bindPhoneSmsTimer);\n\t\t\t\t\tthis.bindPhoneSmsTimer = null;\n\t\t\t\t\tthis.bindPhoneSmsCountdown = 0;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 验证手机号\n\t\t\tvalidatePhone(phone) {\n\t\t\t\tconst phoneReg = /^1[3-9]\\d{9}$/;\n\t\t\t\treturn phoneReg.test(phone);\n\t\t\t},\n\n\t\t\t// 发送绑定手机号验证码\n\t\t\tasync sendBindPhoneSmsCode() {\n\t\t\t\tif (this.bindPhoneSmsCountdown > 0) return;\n\n\t\t\t\tconst phone = this.bindPhoneForm.phone;\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\treturn this.showToast('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\t// 调用发送验证码接口\n\t\t\t\t\tconst response = await this.$api.base.sendSmsCode({ phone });\n\n\t\t\t\t\tif (response.code === '200') {\n\t\t\t\t\t\tthis.showToast('验证码发送成功', 'success');\n\t\t\t\t\t\tthis.startBindPhoneCountdown();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.showToast(response.msg || '验证码发送失败，请重试');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('发送验证码失败:', error);\n\t\t\t\t\tthis.showToast('验证码发送失败，请重试');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 开始绑定手机号倒计时\n\t\t\tstartBindPhoneCountdown() {\n\t\t\t\tthis.bindPhoneSmsCountdown = 60;\n\t\t\t\tthis.bindPhoneSmsTimer = setInterval(() => {\n\t\t\t\t\tthis.bindPhoneSmsCountdown--;\n\t\t\t\t\tif (this.bindPhoneSmsCountdown <= 0) {\n\t\t\t\t\t\tclearInterval(this.bindPhoneSmsTimer);\n\t\t\t\t\t\tthis.bindPhoneSmsTimer = null;\n\t\t\t\t\t}\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\n\t\t\tnavigateToAgreement(type) {\n\t\t\t\tlet url = '../user/configuser';\n\t\t\t\tif (type === 'service') {\n\t\t\t\t\turl += '?type=service';\n\t\t\t\t} else if (type === 'privacy') {\n\t\t\t\t\turl += '?type=privacy';\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 处理绑定手机号\n\t\t\tasync handleBindPhone() {\n\t\t\t\tif (!this.canBindPhone || this.isBindingPhone) return;\n\n\t\t\t\tconst { phone, code } = this.bindPhoneForm;\n\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\treturn this.showToast('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\tif (!code) {\n\t\t\t\t\treturn this.showToast('请输入验证码');\n\t\t\t\t}\n\n\t\t\t\tthis.isBindingPhone = true;\n\t\t\t\tuni.showLoading({ title: '绑定中...' });\n\n\t\t\t\ttry {\n\t\t\t\t\t// 获取unionid\n\t\t\t\t\tconst unionid = uni.getStorageSync('unionid');\n\t\t\t\t\tif (!unionid) {\n\t\t\t\t\t\tthrow new Error('缺少微信用户标识，请重新登录');\n\t\t\t\t\t}\n\t\t\t\tconst registerID=\tuni.getStorageSync(\"registerID\")\n\t\t\t\t\t// 调用绑定接口\n\t\t\t\t\tconst params = {\n\t\t\t\t\t\tphone: phone,\n\t\t\t\t\t\tshortCode: code,\n\t\t\t\t\t\tunionid: unionid,\n\t\t\t\t\t\tplatform: 2, // 用户端\n\t\t\t\t\t\tregistrationId: registerID // 极光推送id，暂时为空\n\t\t\t\t\t};\n\n\t\t\t\t\tconsole.log('绑定手机号参数:', params);\n\n\t\t\t\t\tconst response = await this.$api.user.register(params);\n\t\t\t\t\tconsole.log('绑定手机号响应:', response);\n\n\t\t\t\t\tif (response.code === '200') {\n\t\t\t\t\t\tthis.showToast('绑定成功', 'success');\n\n\t\t\t\t\t\t// 更新用户信息\n\t\t\t\t\t\tconst updatedUserInfo = {\n\t\t\t\t\t\t\t...this.storeUserInfo,\n\t\t\t\t\t\t\tphone: phone\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: updatedUserInfo\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 更新本地存储\n\t\t\t\t\t\tuni.setStorageSync('phone', phone);\n\n\t\t\t\t\t\t// 关闭弹窗\n\t\t\t\t\t\tthis.hideBindPhonePopup();\n\n\t\t\t\t\t\t// 刷新用户信息\n\t\t\t\t\t\tthis.fetchUserInfo();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response.msg || '绑定失败，请重试');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('绑定手机号失败:', error);\n\t\t\t\t\tthis.showToast(error.message || '绑定失败，请重试');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isBindingPhone = false;\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\tinitUserData() {\n\t\t\t\tconsole.log('initUserData被调用');\n\t\t\t\tconsole.log('当前token:', this.token);\n\t\t\t\tconsole.log('当前storeUserInfo:', this.storeUserInfo);\n\n\t\t\t\tif (this.token && !this.storeUserInfo.userId) {\n\t\t\t\t\tconsole.log('有token但Vuex中没有用户信息，从本地存储恢复');\n\n\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\tphone: uni.getStorageSync('phone') || '',\n\t\t\t\t\t\tavatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\tuserId: uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: uni.getStorageSync('pid') || '',\n\t\t\t\t\t\tunionid: uni.getStorageSync('unionid') || ''\n\t\t\t\t\t};\n\n\t\t\t\t\tconsole.log('从本地存储获取的用户信息:', userInfo);\n\n\t\t\t\t\t// 只要有userId就保存用户信息（微信登录可能没有手机号）\n\t\t\t\t\tif (userInfo.userId) {\n\t\t\t\t\t\tconsole.log('保存用户信息到Vuex');\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('没有userId，处理无效会话');\n\t\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t\t}\n\t\t\t\t} else if (this.token && this.storeUserInfo.userId) {\n\t\t\t\t\tconsole.log('token和用户信息都存在，无需初始化');\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('没有token，跳过初始化');\n\t\t\t\t}\n\t\t\t},\n\t\t\tnavigateTo(url) {\n\t\t\t\tif (!url) return;\n\t\t\t\tconst requiresLogin = [\n\t\t\t\t\t// '../user/coupon',\n\t\t\t\t\t// '../user/repair_record',\n\t\t\t\t\t// '../user/order_list',\n\t\t\t\t\t// '../user/address',\n\t\t\t\t\t// '../user/Settle',\n\t\t\t\t\t// '../user/agent_apply',\n\t\t\t\t\t// '../user/promotion',\n\t\t\t\t\t// '../user/bankCard',\n\t\t\t\t\t// '../shifu/Settle',\n\t\t\t\t\t// '../shifu/Receiving',\n\t\t\t\t\t// '../shifu/mine'\n\t\t\t\t];\n\t\t\t\tif (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {\n\t\t\t\t\treturn this.showToast('请先登录');\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t});\n\t\t\t},\n\t\t\tfetchUserInfo() {\n\t\t\t\tif (this.isLoading || !this.token) {\n\t\t\t\t\tconsole.log('Skipping fetchUserInfo: no token or already loading');\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t}\n\t\t\t\tthis.isLoading = true;\n\t\t\t\treturn this.$api.user.userInfo()\n\t\t\t\t\t.then(responses => {\n\t\t\t\t\t\tlet response=responses.data\n\t\t\t\t\t\tif (!response || typeof response !== 'object') {\n\t\t\t\t\t\t\tthrow new Error('获取用户信息失败: 响应数据无效');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\tphone: response.phone || '',\n\t\t\t\t\t\t\tavatarUrl: response.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\t\t\tnickName: response.nickName || '微信用户',\n\t\t\t\t\t\t\tuserId: response.id || '',\n\t\t\t\t\t\t\tcreateTime: response.createTime || '',\n\t\t\t\t\t\t\tpid: response.pid || '',\n\t\t\t\t\t\t\tinviteCode: response.inviteCode || ''\n\t\t\t\t\t\t};\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tconsole.error('获取用户信息失败:', error);\n\t\t\t\t\t\tif (error.message && error.message.includes('响应数据无效')) {\n\t\t\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (this.token) {\n\t\t\t\t\t\t\t\tthis.showToast('获取用户信息失败，请稍后重试');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t});\n\t\t\t},\n\t\t\tsaveUserInfoToStorage(userInfo) {\n\t\t\t\tuni.setStorageSync('phone', userInfo.phone);\n\t\t\t\tuni.setStorageSync('avatarUrl', userInfo.avatarUrl);\n\t\t\t\tuni.setStorageSync('nickName', userInfo.nickName);\n\t\t\t\tuni.setStorageSync('userId', userInfo.userId);\n\t\t\t\tuni.setStorageSync('pid', userInfo.pid);\n\t\t\t\tif (userInfo.unionid) {\n\t\t\t\t\tuni.setStorageSync('unionid', userInfo.unionid);\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleInvalidSession() {\n\t\t\t\t['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid', 'unionid'].forEach(key => {\n\t\t\t\t\tuni.removeStorageSync(key);\n\t\t\t\t});\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: {}\n\t\t\t\t});\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\tval: ''\n\t\t\t\t});\n\t\t\t\tthis.isLoading = false;\n\t\t\t\t// Reset orderList counts to 0 when session is invalid\n\t\t\t\tthis.$set(this, 'orderList', this.orderList.map(item => ({\n\t\t\t\t\t...item,\n\t\t\t\t\tcount: 0\n\t\t\t\t})));\n\t\t\t},\n\t\t\tonGetPhoneNumber(e) {\n\t\t\t\tconsole.log('微信授权登录');\n\t\t\t\tif (e.detail.errMsg !== 'getPhoneNumber:ok') {\n\t\t\t\t\treturn this.showToast('授权失败，请重试');\n\t\t\t\t}\n\t\t\t\tthis.getmylogin();\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tuni.showLoading({\n\t\t\t\t\tmask: true,\n\t\t\t\t\ttitle: '登录中...'\n\t\t\t\t});\n\t\t\t\tconst {\n\t\t\t\t\tencryptedData,\n\t\t\t\t\tiv\n\t\t\t\t} = e.detail;\n\t\t\t\tuni.checkSession({\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tthis.loginWithWeixin({\n\t\t\t\t\t\t\tcode: this.code,\n\t\t\t\t\t\t\tencryptedData,\n\t\t\t\t\t\t\tiv,\n\t\t\t\t\t\t\tplatform:2,\n\t\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.login({\n\t\t\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\t\t\t\tconsole.log('Refreshed wx.login code:', this.code);\n\t\t\t\t\t\t\t\t\tthis.loginWithWeixin({\n\t\t\t\t\t\t\t\t\t\tcode: this.code,\n\t\t\t\t\t\t\t\t\t\tencryptedData,\n\t\t\t\t\t\t\t\t\t\tiv,\n\t\t\t\t\t\t\t\t\t\tplatform:2,\n\t\t\t\t\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tthis.showToast('获取登录凭证失败');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.showToast('微信登录失败，请重试');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tloginWithWeixin(params) {\n\t\t\t\t// 获取平台类型\n\t\t\t\tconst isapp = this.getCurrentPlatform() === 'app-plus' ? 1 : 0;\n\t\t\t\tconsole.log('微信小程序登录平台类型 isapp:', isapp);\n\n\t\t\t\tthis.$api.user.loginuserInfo({\n\t\t\t\t\t\tcode: params.code,\n\t\t\t\t\t\tencryptedData: params.encryptedData,\n\t\t\t\t\t\tiv: params.iv,\n\t\t\t\t\t\tplatform:2,\n\t\t\t\t\t\tpid: this.inviteCode,\n\t\t\t\t\t\tisapp: isapp // 添加平台类型参数\n\t\t\t\t\t})\n\t\t\t\t\t.then(response => {\n\t\t\t\t\t\tconsole.log(response)\n\t\t\t\t\t\tif (!response || !response.data.token) {\n\t\t\t\t\t\t\tthrow new Error('请重新登录');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.setStorageSync('token', response.data.token);\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\t\t\tval: response.data.token\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn this.$api.user.userInfo();\n\t\t\t\t\t})\n\t\t\t\t\t.then(userInfoRess => {\n\t\t\t\t\t\tlet userInfoRes=userInfoRess.data\n\t\t\t\t\t\tif (!userInfoRes || typeof userInfoRes !== 'object') {\n\t\t\t\t\t\t\tthrow new Error('获取用户信息失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\tphone: userInfoRes.phone || '',\n\t\t\t\t\t\t\tavatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\t\t\tnickName: userInfoRes.nickName || '微信用户',\n\t\t\t\t\t\t\tuserId: userInfoRes.id || '',\n\t\t\t\t\t\t\tcreateTime: userInfoRes.createTime || '',\n\t\t\t\t\t\t\tpid: userInfoRes.pid || ''\n\t\t\t\t\t\t};\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t\tthis.showToast('登录成功', 'success');\n\t\t\t\t\t\tthis.hideLoginPopup();\n\t\t\t\t\t\tthis.debounceGetHighlight();\n\t\t\t\t\t\t// this.getshifuinfo();\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tconsole.error('Login error:', error);\n\t\t\t\t\t\tthis.showToast(error.message || '登录失败，请稍后重试');\n\t\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t\t})\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\tshowToast(title, icon = 'none') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle,\n\t\t\t\t\ticon,\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n\t/* Login Popup Styles */\n\t.login-popup-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tz-index: 2000;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t}\n\n\t.login-popup {\n\t\tbackground-color: #fff;\n\t\twidth: 100%;\n\t\tborder-radius: 40rpx 40rpx 0 0;\n\t\tposition: relative;\n\t\tmax-height: 60vh;\n\t\tpadding-bottom: 10rpx;\n\t\tanimation: slideUp 0.3s ease-out;\n\t}\n\n\t@keyframes slideUp {\n\t\tfrom {\n\t\t\ttransform: translateY(100%);\n\t\t}\n\n\t\tto {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.close-btn {\n\t\tposition: absolute;\n\t\ttop: 30rpx;\n\t\tright: 30rpx;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: #999;\n\t\tfont-size: 40rpx;\n\t\tz-index: 10;\n\t}\n\n\t.popup-content {\n\t\tpadding: 80rpx 60rpx 40rpx;\n\t\ttext-align: center;\n\t}\n\n\t.welcome-title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.welcome-subtitle {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 80rpx;\n\t}\n\n\t.agreement-section {\n\t\tmargin-bottom: 60rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\n\t.checkbox-container {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\ttext-align: left;\n\t\tmax-width: 560rpx;\n\t}\n\n\t.checkbox {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder: 2rpx solid #ddd;\n\t\tborder-radius: 6rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 20rpx;\n\t\tmargin-top: 4rpx;\n\t\tflex-shrink: 0;\n\t\tbackground-color: #fff;\n\t\ttransition: all 0.2s;\n\n\t\t&.checked {\n\t\t\tbackground-color: #00C853;\n\t\t\tborder-color: #00C853;\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\t.iconfont {\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n\n\t.agreement-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\n\t\t.link {\n\t\t\tcolor: #00C853;\n\t\t}\n\t}\n\n\t.phone-login-btn {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tbackground: linear-gradient(135deg, #00C853, #4CAF50);\n\t\tborder: none;\n\t\tborder-radius: 50rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);\n\t\ttransition: all 0.2s;\n\n\t\t&:active:not(.disabled) {\n\t\t\ttransform: translateY(2rpx);\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);\n\t\t}\n\n\t\t&.disabled {\n\t\t\tbackground: #ccc;\n\t\t\tbox-shadow: none;\n\t\t\topacity: 0.6;\n\t\t}\n\n\t\t&::after {\n\t\t\tborder: none;\n\t\t}\n\t}\n\n\t.bind-phone-container {\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.bind-phone-btn {\n\t\tbackground: none;\n\t\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\n\t\tborder-radius: 32rpx;\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tline-height: 1.5;\n\t\tpadding: 8rpx 24rpx;\n\t\ttransition: all 0.2s;\n\n\t\t&:active:not([disabled]) {\n\t\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\t\ttransform: scale(0.98);\n\t\t}\n\n\t\t&[disabled] {\n\t\t\topacity: 0.6;\n\t\t}\n\n\t\t&::after {\n\t\t\tborder: none;\n\t\t}\n\t}\n\n\t.input-group {\n\t\tmargin-bottom: 40rpx;\n\n\t\t.input-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tbackground: #f8fafc;\n\t\t\tborder: 2rpx solid #e2e8f0;\n\t\t\tborder-radius: 16rpx;\n\t\t\tmargin-bottom: 24rpx;\n\t\t\tmin-height: 88rpx;\n\t\t\tpadding: 0;\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t&:focus-within {\n\t\t\t\tborder-color: #3b82f6;\n\t\t\t\tbox-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.08);\n\t\t\t}\n\n\t\t\t.input-icon {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-left: 24rpx;\n\t\t\t\tbackground: rgba(59, 130, 246, 0.08);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\n\t\t\t.input-field {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-left: 24rpx;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #1e293b;\n\t\t\t\tbackground: transparent;\n\t\t\t\tborder: none;\n\t\t\t\toutline: none;\n\n\t\t\t\t&::placeholder {\n\t\t\t\t\tcolor: #94a3b8;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.sms-btn {\n\t\t\t\tbackground: linear-gradient(135deg, #3b82f6, #1d4ed8);\n\t\t\t\tcolor: #fff;\n\t\t\t\tpadding: 16rpx 24rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);\n\t\t\t\tflex-shrink: 0;\n\n\t\t\t\t&.disabled {\n\t\t\t\t\tbackground: #cbd5e1;\n\t\t\t\t\tcolor: #64748b;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\n\t\t\t\t&:not(.disabled):active {\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.alternative-login {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 40rpx;\n\n\t\t.divider-line {\n\t\t\tflex: 1;\n\t\t\theight: 1rpx;\n\t\t\tbackground-color: #eee;\n\t\t}\n\n\t\t.divider-text {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #999;\n\t\t\tmargin: 0 30rpx;\n\t\t}\n\t}\n\n\t.sms-login-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground-color: #fff;\n\t\tborder: 2rpx solid #ddd;\n\t\tborder-radius: 44rpx;\n\t\tcolor: #666;\n\t\tfont-size: 32rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s;\n\n\t\t&:active {\n\t\t\tbackground-color: #f8f8f8;\n\t\t\tborder-color: #00C853;\n\t\t}\n\n\t\t&::after {\n\t\t\tborder: none;\n\t\t}\n\n\t\t.iconfont {\n\t\t\tmargin-right: 16rpx;\n\t\t\tfont-size: 36rpx;\n\t\t}\n\t}\n\n\t/* Floating Contact Button Styles */\n\t.floating-contact {\n\t\tposition: fixed;\n\t\tbottom: 150rpx;\n\t\tright: 30rpx;\n\t\tz-index: 1000;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 50rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-btn {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcolor: #576b95;\n\t\tfont-size: 30rpx;\n\t\tline-height: 1.5;\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-btn:active {\n\t\tbackground-color: #ededee;\n\t}\n\n\t/* Existing Styles */\n\t.pages-mine {\n\t\tbackground-color: #f8f8f8;\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: 120rpx;\n\n\t\t.header {\n\t\t\theight: 292rpx;\n\t\t\tbackground-color: #599EFF;\n\t\t\tposition: relative;\n\n\t\t\t.header-content {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 40rpx 30rpx 0;\n\n\t\t\t\t.avatar_view {\n\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\theight: 120rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n\n\t\t\t\t\t.avatar {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.user-info {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tcolor: #fff;\n\n\t\t\t\t\t.user-info-logged {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.nickname {\n\t\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\n\t\t\t\t\t.phone-number {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t}\n\n\t\t\t\t\tbutton {\n\t\t\t\t\t\tbackground: none;\n\t\t\t\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.5);\n\t\t\t\t\t\tborder-radius: 32rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\t\tpadding: 10rpx 30rpx;\n\n\t\t\t\t\t\t&.loading {\n\t\t\t\t\t\t\topacity: 0.7;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.settings {\n\t\t\t\t\tpadding: 10rpx;\n\n\t\t\t\t\t.icon-xitong {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.box1 {\n\t\t\tmargin-top: -20rpx;\n\t\t\tborder-radius: 36rpx 36rpx 0 0;\n\t\t\tposition: relative;\n\t\t\tz-index: 10;\n\t\t}\n\n\t\t.mine-menu-list {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\n\t\t\t.menu-title {\n\t\t\t\theight: 90rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 0 30rpx 0 40rpx;\n\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\n\t\t\t\t.f-paragraph {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.flex-warp {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tpadding: 30rpx 0;\n\n\t\t\t\t.order-item {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\twidth: 25%;\n\t\t\t\t\tfont-size: 25rpx;\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\ttransition: transform 0.2s;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t}\n\n\t\t\t\t\t.icon-container {\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t.number-circle {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: -10rpx;\n\t\t\t\t\t\tright: -5rpx;\n\t\t\t\t\t\twidth: 30rpx;\n\t\t\t\t\t\theight: 30rpx;\n\t\t\t\t\t\tbackground-color: #ff4d4f;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.mt-sm {\n\t\t\t\t\tmargin-top: 16rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.spacer {\n\t\t\theight: 20rpx;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t.mine-tool-list {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx 30rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\t\toverflow: hidden;\n\t\t\tpadding: 20rpx 10rpx;\n\n\t\t\t.flex-warp {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\t/* Changed from space-between to flex-start for left alignment */\n\t\t\t}\n\n\t\t\t.list-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\twidth: 25%;\n\t\t\t\t/* Adjusted width to ensure items align left and fit more naturally */\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\ttransition: background-color 0.2s;\n\n\t\t\t\t&.master-side {\n\t\t\t\t\t.item-text {\n\t\t\t\t\t\tcolor: #E41F19;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:active {\n\t\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\t}\n\n\t\t\t\t.item-text {\n\t\t\t\t\tmargin-top: 16rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.flex-between {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754788677768\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}