@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-42e2fba5 {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
  padding: 40rpx 0;
  padding-bottom: 200rpx;
  /* 调整最后一个 radio 为大尺寸 */
  /* 底部结算栏样式 */
}
.page .small-radio.data-v-42e2fba5 {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center;
          transform-origin: center;
}
.page .large-radio.data-v-42e2fba5 {
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-transform-origin: center;
          transform-origin: center;
}
.page .car_item.data-v-42e2fba5 {
  margin: 0 auto;
  width: 686rpx;
  background: #FFFFFF;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
  position: relative;
}
.page .car_item .trash.data-v-42e2fba5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}
.page .car_item .trash .checkbox.data-v-42e2fba5 {
  display: flex;
  align-items: center;
}
.page .car_item .trash .checkbox .name.data-v-42e2fba5 {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #333;
}
.page .car_item .divider.data-v-42e2fba5 {
  height: 2rpx;
  background-color: #F2F3F6;
  margin: 0 -20rpx;
}
.page .car_item .top.data-v-42e2fba5 {
  padding: 36rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #F2F3F6;
}
.page .car_item .top .checkbox.data-v-42e2fba5 {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
}
.page .car_item .top image.data-v-42e2fba5 {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
}
.page .car_item .top .right.data-v-42e2fba5 {
  flex: 1;
}
.page .car_item .top .right .name.data-v-42e2fba5 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
}
.page .car_item .top .right .choose ._span.data-v-42e2fba5 {
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  margin-right: 4rpx;
}
.page .car_item .top .right .choose ._span.last-item.data-v-42e2fba5 {
  margin-right: 0;
}
.page .car_item .top .right .price.data-v-42e2fba5 {
  width: 100%;
  margin-top: 68rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #E72427;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .footer.data-v-42e2fba5 {
  position: fixed;
  bottom: 130rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #f2f2f2;
  padding: 0 60rpx;
  padding-right: 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.page .footer .footer-left.data-v-42e2fba5 {
  flex: 1;
  display: flex;
  align-items: center;
}
.page .footer .footer-left .checkbox.data-v-42e2fba5 {
  display: flex;
  align-items: center;
}
.page .footer .footer-left .checkbox text.data-v-42e2fba5 {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #333;
}
.page .footer .footer-center.data-v-42e2fba5 {
  flex: 2;
  text-align: right;
  padding-right: 30rpx;
}
.page .footer .footer-center text.data-v-42e2fba5 {
  font-size: 28rpx;
  color: #333;
}
.page .footer .footer-center .total-price.data-v-42e2fba5 {
  font-size: 32rpx;
  font-weight: bold;
  color: #E72427;
}
.page .footer .footer-right.data-v-42e2fba5 {
  width: 200rpx;
  height: 80rpx;
  background-color: #2979ff;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
/* 弹窗样式 */
.modal-overlay.data-v-42e2fba5 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  /* Align to the bottom */
  justify-content: center;
  z-index: 9999;
}
.modal-container.data-v-42e2fba5 {
  width: 100%;
  /* Full width */
  max-height: 80vh;
  /* Max height 80% of viewport height */
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  /* Rounded corners only at the top */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
  /* Start off-screen at the bottom */
  -webkit-animation: slide-up-data-v-42e2fba5 0.3s forwards ease-out;
          animation: slide-up-data-v-42e2fba5 0.3s forwards ease-out;
  /* Animation for sliding up */
}
@-webkit-keyframes slide-up-data-v-42e2fba5 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slide-up-data-v-42e2fba5 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.modal-header.data-v-42e2fba5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
  background: #fff;
  position: -webkit-sticky;
  position: sticky;
  /* Keep header at the top */
  top: 0;
  z-index: 10;
}
.modal-title.data-v-42e2fba5 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.close-btn.data-v-42e2fba5 {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}
.loading-container.data-v-42e2fba5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  flex-grow: 1;
  /* Allow it to take available space */
}
.loading-container text.data-v-42e2fba5 {
  font-size: 28rpx;
  color: #666;
}
.modal-scroll.data-v-42e2fba5 {
  flex: 1;
  /* Allow scroll-view to take up remaining height */
  overflow-y: auto;
}
.config-content.data-v-42e2fba5 {
  padding: 20rpx;
}
.card.data-v-42e2fba5 {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.card .bottom.data-v-42e2fba5 {
  display: flex;
}
.card .bottom .left.data-v-42e2fba5 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  padding-top: 10rpx;
}
.card .bottom .right.data-v-42e2fba5 {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.card .bottom .right .tag.data-v-42e2fba5 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 44rpx;
  padding: 0 12rpx;
  background: #DCEAFF;
  border-radius: 4rpx;
  font-size: 16rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 44rpx;
  text-align: center;
  margin: 10rpx;
}
.chol .choose.data-v-42e2fba5 {
  padding: 30rpx 0;
}
.chol .choose .title.data-v-42e2fba5 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.chol .choose .title ._span.data-v-42e2fba5 {
  color: #E72427;
}
.chol .choose .input-container.data-v-42e2fba5 {
  margin-top: 30rpx;
  position: relative;
  width: 100%;
  min-height: 80rpx;
}
.chol .choose .form-input.data-v-42e2fba5 {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  line-height: 80rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}
.chol .choose .form-input.data-v-42e2fba5:focus {
  background: #fff;
  border-color: #2E80FE;
  box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
  outline: none;
}
.chol .choose .desc.data-v-42e2fba5 {
  margin-top: 15rpx;
  font-size: 22rpx;
  font-weight: 400;
  color: #ADADAD;
}
.chol .choose .up.data-v-42e2fba5 {
  margin-bottom: 30rpx;
}
.chol .choose .cho_box.data-v-42e2fba5 {
  margin-top: 15rpx;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 15rpx;
  /* Add some padding to the bottom */
}
.chol .choose .cho_box .box_item.data-v-42e2fba5 {
  width: -webkit-fit-content;
  width: fit-content;
  padding: 0 15rpx;
  height: 50rpx;
  background: #FFFFFF;
  border-radius: 4rpx;
  border: 2rpx solid #D8D8D8;
  font-size: 22rpx;
  font-weight: 400;
  color: #ADADAD;
  line-height: 50rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  position: relative;
}
.chol .choose .cho_box .box_item .ok.data-v-42e2fba5 {
  width: 18rpx;
  height: 18rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #2E80FE;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chol .fg.data-v-42e2fba5 {
  width: 100%;
  height: 15rpx;
  background: #F3F4F5;
  margin: 15rpx 0;
}
.modal-footer.data-v-42e2fba5 {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f2f2f2;
  gap: 20rpx;
  position: -webkit-sticky;
  position: sticky;
  /* Keep footer at the bottom */
  bottom: 0;
  background: #fff;
  z-index: 10;
}
.modal-btn.data-v-42e2fba5 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 400;
}
.modal-btn.cancel.data-v-42e2fba5 {
  background: #f5f5f5;
  color: #666;
}
.modal-btn.confirm.data-v-42e2fba5 {
  background: #2e80fe;
  color: #ffffff;
  transition: all 0.2s ease;
}
.modal-btn.confirm.submitting.data-v-42e2fba5 {
  background: #8bb8ff;
  opacity: 0.7;
  pointer-events: none;
}

