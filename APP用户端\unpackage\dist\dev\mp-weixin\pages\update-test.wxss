@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.test-page.data-v-41386fa6 {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-41386fa6 {
  text-align: center;
  padding: 40rpx 0;
}
.header .title.data-v-41386fa6 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-41386fa6 {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.test-section .section-title.data-v-41386fa6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}
.info-item.data-v-41386fa6 {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-41386fa6:last-child {
  border-bottom: none;
}
.info-item .label.data-v-41386fa6 {
  color: #666;
  font-size: 28rpx;
}
.info-item .value.data-v-41386fa6 {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}
.test-btn.data-v-41386fa6 {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}
.test-btn.data-v-41386fa6:last-child {
  margin-bottom: 0;
}
.test-btn.data-v-41386fa6:disabled {
  background: #ccc;
}
.test-btn.data-v-41386fa6:active:not(:disabled) {
  opacity: 0.8;
}
.log-container.data-v-41386fa6 {
  height: 400rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background: #fafafa;
}
.log-container .log-item.data-v-41386fa6 {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-41386fa6 {
  color: #999;
  margin-right: 20rpx;
  min-width: 120rpx;
}
.log-container .log-item .log-content.data-v-41386fa6 {
  color: #333;
  flex: 1;
  word-break: break-all;
}
.clear-btn.data-v-41386fa6 {
  width: 100%;
  height: 60rpx;
  background: #f56565;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-top: 20rpx;
}
.clear-btn.data-v-41386fa6:active {
  opacity: 0.8;
}

