<view class="page data-v-458b6785"><block wx:if="{{flag}}"><u-picker vue-id="6be20a21-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeH<PERSON>ler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-458b6785 vue-ref" bind:__l="__l"></u-picker></block><u-modal vue-id="6be20a21-2" show="{{show}}" title="{{title}}" showCancelButton="{{true}}" confirmText="同意" cancelText="不同意" data-event-opts="{{[['^confirm',[['confirmModel']]],['^cancel',[['cancelModel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-458b6785" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-458b6785"><rich-text nodes="{{configInfos.entryNotice?configInfos.entryNotice:getentryNotices}}" class="data-v-458b6785"></rich-text></view></u-modal><block wx:if="{{shInfo.status==4}}"><u-modal vue-id="6be20a21-3" show="{{showSh}}" title="驳回原因" confirmText="确定" content="{{shInfo.shText}}" data-event-opts="{{[['^confirm',[['e1']]]]}}" bind:confirm="__e" class="data-v-458b6785" bind:__l="__l"></u-modal></block><u-modal vue-id="6be20a21-4" show="{{showSubmitConfirm}}" title="提交确认" showCancelButton="{{true}}" confirmText="确认" cancelText="取消" data-event-opts="{{[['^confirm',[['confirmSubmit']]],['^cancel',[['cancelSubmit']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-458b6785" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-458b6785"><text class="data-v-458b6785">确定要提交信息吗？</text></view></u-modal><block wx:if="{{shInfo.status!==-1}}"><view data-event-opts="{{[['tap',[['shDetail',['$event']]]]]}}" class="header data-v-458b6785" style="{{('color:'+arr[shInfo.status-1].color)}}" bindtap="__e">{{''+arr[shInfo.status-1].text+''}}</view></block><view class="main data-v-458b6785"><view class="main_item data-v-458b6785"><view class="title data-v-458b6785"><label class="_span data-v-458b6785">*</label>姓名</view><input type="text" placeholder="请输入姓名" disabled="{{isFormDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','coachName','$event',[]],['form']]]]]}}" value="{{form.coachName}}" bindinput="__e" class="data-v-458b6785"/></view><view class="main_item data-v-458b6785"><view class="title data-v-458b6785"><label class="_span data-v-458b6785">*</label>手机号</view><input type="text" placeholder="请输入手机号" disabled="{{isFormDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e" class="data-v-458b6785"/></view><view class="main_item data-v-458b6785"><view class="title data-v-458b6785"><label class="_span data-v-458b6785">*</label>选择区域</view><input class="{{['data-v-458b6785',(!isFormDisabled)?'input-disabled':'']}}" type="text" placeholder="请选择代理区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e2',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['goMap',['$event']]]]]}}" class="main_item data-v-458b6785" bindtap="__e"><view class="title data-v-458b6785"><label class="_span data-v-458b6785">*</label>详细地址</view><view class="address data-v-458b6785"><input placeholder="请点击选取地址" disabled="{{isFormDisabled}}" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form']]]]]}}" value="{{form.address}}" bindinput="__e" class="data-v-458b6785"/></view></view></view><block wx:if="{{shInfo.status===-1||shInfo.status===4}}"><view class="footer data-v-458b6785"><view data-event-opts="{{[['tap',[['showConfirmPopup',['$event']]]]]}}" class="btn data-v-458b6785" bindtap="__e">立即提交</view></view></block></view>