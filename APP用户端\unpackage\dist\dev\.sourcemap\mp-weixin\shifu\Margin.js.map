{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Margin.vue?707c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Margin.vue?d2b9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Margin.vue?d1dc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Margin.vue?352b", "uni-app:///shifu/Margin.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Margin.vue?8591", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Margin.vue?7a78"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "methods", "getMoney", "submit", "uni", "icon", "title", "confirmPay", "payPrice", "couponId", "type", "console", "timeStamp", "nonceStr", "package", "signType", "paySign", "partnerid", "appId", "success", "setTimeout", "url", "fail", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCYz2B;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;QACA;QACA;MAEA;IACA;IACAC;MACA;QACA;MACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;MACA;QACAC;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAMA;UACAC;UAAA;UACAC;UACAC;UACAC;UACAC;QACA;QACAL;QAEAP;UACA;UAEAQ;UACAC;UACAC;UACAG;UAEAF;UACAC;UACAE;UACA;UACAC;YACA;YACAR;YACAP;cACAE;cACAD;YACA;YACAe;cACAhB;gBACAiB;cACA;YACA;UACA;UACAC;YACA;YACAX;;YAEA;YACAA;;YAEA;YACA;cACAjB;gBACAY;gBACAD;cACA;YACA;cACAX;gBACAY;gBACAD;cACA;YACA;;YAEA;YACAM;YACAP;cACAE;cACAD;YACA;UAEA;QACA;MACA;IACA;EACA;EACAkB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/HA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/Margin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/Margin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Margin.vue?vue&type=template&id=92a41b30&scoped=true&\"\nvar renderjs\nimport script from \"./Margin.vue?vue&type=script&lang=js&\"\nexport * from \"./Margin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92a41b30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/Margin.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=template&id=92a41b30&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"box\">\r\n\t\t\t<view class=\"money\">{{money}}</view>\r\n\t\t\t<view class=\"title\">保证金金额（元）</view>\r\n\t\t\t<!-- <view class=\"btn\" @click=\"submit\">缴纳保证金</view> -->\r\n\t\t\t<view class=\"btn\" @click=\"submit\">缴纳保证金</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmoney: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetMoney() {\r\n\t\t\t\tthis.$api.shifu.seeBzj().then(res => {\r\n\t\t\t\t\tif(res===-1){\r\n\t\t\t\t\t\t\tthis.money = 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.money = res\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmit() {\r\n\t\t\t\tif (this.money == 0 || this.money == '') {\r\n\t\t\t\t\tthis.confirmPay()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '您已缴纳保证金快去接单吧'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tconfirmPay() {\r\n\t\t\t\tthis.$api.shifu.nowPay({\r\n\t\t\t\t\tpayPrice :200,\r\n\t\t\t\t\tcouponId: 0,\r\n\t\t\t\t\ttype:1,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tlet obj = res\r\n\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\r\n\t\t\t\t\tconsole.log(String(packageStr))\r\n\t\t\t\t\tconsole.log(obj.nonceStr)\r\n\t\t\t\t\tconsole.log(packageStr)\r\n\t\t\t\t\tconsole.log(obj.nonceStr)\r\n\t\t\t\t\tconsole.log(String(obj.timestamp))\r\n\t\t\t\t\tconsole.log(obj.sign)\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t\t\tconst paymentParams = {\r\n\t\t\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string \r\n\t\t\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\t\t\tsignType: 'MD5',\r\n\t\t\t\t\t\tpaySign: obj.sign\r\n\t\t\t\t\t};\r\n\t\t\t\t\tconsole.log(JSON.stringify(paymentParams));\r\n\r\n\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\"provider\": 'wxpay',\r\n\r\n\t\t\t\t\t\ttimeStamp: String(obj.timestamp),\r\n\t\t\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\t\t\tpartnerid: obj.partnerId,\r\n\r\n\t\t\t\t\t\tsignType: \"MD5\",\r\n\t\t\t\t\t\tpaySign: obj.sign,\r\n\t\t\t\t\t\tappId: obj.appId,\r\n\t\t\t\t\t\t// total_fee:\"10\",\r\n\t\t\t\t\t\tsuccess: (res1) => {\r\n\t\t\t\t\t\t\t// 支付成功回调\r\n\t\t\t\t\t\t\tconsole.log('支付成功', res1);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\turl: '/shifu/Margin'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t// 1) 直接打印对象（在控制台里能展开查看）\r\n\t\t\t\t\t\t\tconsole.error('requestPayment fail object:', err);\r\n\r\n\t\t\t\t\t\t\t// 2) 或者打印序列化后的 JSON 字符串\r\n\t\t\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\r\n\r\n\t\t\t\t\t\t\t// 根据 err.errMsg 做判断\r\n\t\t\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\r\n\t\t\t\t\t\t\t\twx.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '您已取消支付',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\twx.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 支付失败回调\r\n\t\t\t\t\t\t\tconsole.error('支付失败', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付失败请检查网络',\r\n\t\t\t\t\t\t\t\ticon: 'error'\r\n\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getMoney()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.page {\r\n\t\tbackground: #F8F8F8;\r\n\t\theight: 100vh;\r\n\r\n\t\t.box {\r\n\t\t\tpadding: 50rpx 82rpx;\r\n\t\t\tbackground: #fff;\r\n\r\n\t\t\t.money {\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\twidth: fit-content;\r\n\t\t\t\tfont-size: 80rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #171717;\r\n\t\t\t}\r\n\r\n\t\t\t.title {\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\twidth: fit-content;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #171717;\r\n\t\t\t}\r\n\r\n\t\t\t.btn {\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tmargin-top: 64rpx;\r\n\t\t\t\twidth: 584rpx;\r\n\t\t\t\theight: 98rpx;\r\n\t\t\t\tbackground: #2E80FE;\r\n\t\t\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\r\n\t\t\t\tline-height: 98rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754788678925\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}