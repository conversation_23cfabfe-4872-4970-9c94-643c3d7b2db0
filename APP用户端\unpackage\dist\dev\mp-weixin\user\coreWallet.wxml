<view class="page data-v-342423c4"><view class="header-stats data-v-342423c4"><view class="stats-card data-v-342423c4"><view class="stats-title data-v-342423c4">已到账总额</view><view class="stats-amount data-v-342423c4">{{"￥"+totalAmount}}</view></view></view><view class="filter-section data-v-342423c4"><u-scroll-list vue-id="505f52b0-1" indicator="{{true}}" indicator-color="#f2f2f2" indicator-active-color="#2e80fe" class="data-v-342423c4" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{statusOptions}}" wx:for-item="item" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['changeStatus',['$0'],[[['statusOptions','value',item.value,'value']]]]]]]}}" class="{{['filter-tab','data-v-342423c4',(currentStatus===item.value)?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></u-scroll-list></view><view class="record-list data-v-342423c4"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['handleRecordTap',['$0'],[[['recordList','id',item.$orig.id]]]]]]]}}" class="record-item data-v-342423c4" bindtap="__e"><view class="record-header data-v-342423c4"><view class="record-amount data-v-342423c4">{{"￥"+item.$orig.amount}}</view><view data-event-opts="{{[['tap',[['handleStatusTap',['$0'],[[['recordList','id',item.$orig.id]]]]]]]}}" class="{{['record-status','data-v-342423c4',item.m0]}}" catchtap="__e">{{''+item.$orig.statusText+''}}<block wx:if="{{item.$orig.lock===1}}"><text class="tap-hint data-v-342423c4">点击提现</text></block><block wx:if="{{item.$orig.lock===0}}"><text class="tap-hint data-v-342423c4">点击取消</text></block></view></view><view class="record-info data-v-342423c4"><view class="record-time data-v-342423c4">{{item.$orig.createTime}}</view></view></view></block></view><block wx:if="{{$root.g0}}"><view class="empty-state data-v-342423c4"><text class="empty-text data-v-342423c4">暂无提现记录</text></view></block><block wx:if="{{$root.g1>0}}"><view class="load-more data-v-342423c4"><block wx:if="{{hasMore&&!loading}}"><text class="load-text data-v-342423c4">上拉加载更多</text></block><block wx:if="{{loading}}"><text class="load-text data-v-342423c4">加载中...</text></block><block wx:if="{{!hasMore&&!loading}}"><text class="load-text data-v-342423c4">没有更多数据了</text></block></view></block></view>