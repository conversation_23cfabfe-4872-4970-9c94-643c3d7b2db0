{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/price_parity.vue?0509", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/price_parity.vue?cc8b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/price_parity.vue?dfd2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/price_parity.vue?34eb", "uni-app:///user/price_parity.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/price_parity.vue?a9ef", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/price_parity.vue?8a7e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "id", "type", "chooseArr", "list", "list2", "list3", "serviceInfo", "form", "tmplIds", "btArr", "focusedInputIndex", "keyboardHeight", "yikoujiaprice", "windowHeight", "isKeyboardShow", "systemInfo", "scrollTimer", "isAddingToCart", "isSubmittingOrder", "showOrderModal", "orderQuantity", "m<PERSON><PERSON><PERSON><PERSON>", "isAddressManuallySelected", "showTimeModal", "currentDate", "currentTime", "conDate", "conTime", "dateArr", "baseTimeArr", "disabled", "time", "time1", "time2", "displayedTimeArr", "week", "is<PERSON><PERSON>", "notes", "computed", "footerStyle", "bottom", "totalPrice", "methods", "dingyue", "console", "templateId", "templateCategoryId", "uni", "success", "title", "content", "cancelText", "confirmText", "confirmColor", "withSubscriptions", "selectedTmplIds", "fail", "handleInputFocus", "clearTimeout", "handleInputBlur", "handleInput", "scrollToInput", "query", "inputRect", "pageScrollInfo", "inputAbsoluteTop", "visibleHeight", "safePosition", "targetScrollTop", "currentScrollTop", "scrollTop", "duration", "imgUpload", "imagelist", "e", "imgtype", "newFormData", "val", "getpzinfo", "res", "item", "serviceId", "name", "choose", "submit", "chooseOne", "getInfo", "contactService", "itemList", "url", "phoneNumber", "goToCart", "confirmAddToCart", "copy_form", "icon", "<PERSON><PERSON><PERSON><PERSON>", "settingId", "num", "setTimeout", "closeOrderModal", "handleOrderClick", "confirmOrder", "open", "pid", "addressId", "urgent", "startTime", "endTime", "text", "couponId", "increaseQuantity", "decreaseQuantity", "toggle<PERSON><PERSON>", "goToAddress", "closeTimeModal", "selectDate", "selectTime", "confirmTime", "updateTimeAvailability", "generateDateArray", "str", "date", "fullDate", "addLeadingZero", "getDefaultAddress", "onLoad", "onKeyboardHeightChange", "onShow", "that", "onHide", "onUnload", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4N/2B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAR;QACAC;MACA;MACAQ,UACA,IACA,IACA,8CACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC,cACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACAC;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;;MACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MACAC;MACA;MACA;QACAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACAC;UACAC;QACA;MAAA;MACAC;QACAvC;QACAwC;UACAJ;UACA;UACA;YAAA;UAAA;UACA;UACA;YACAG;cACAE;cACAC;cACAC;cACAC;cACAC;cACAL;gBACAD;gBACA;kBACAA;oBACAO;kBACA;gBACA;kBACAP;gBACA;cACA;YACA;UACA;UACA;UACAQ;YACAX;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACAY;UACAZ;QACA;MACA;IACA;IACAa;MAAA;MACAb;MACA;MACA;;MAEA;MACA;QACAc;MACA;;MAEA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;IACA;IAEAC;MACAf;MACA;MACA;MACA;;MAEA;MACA;QACAc;QACA;MACA;IACA;IAEAE;MACAhB;IACA;IAEAiB;MAAA;MACA;;MAEA;MACAC;MACAA;MAEAA;QACA;UACA;UACA;UAEAlB;YACAmB;YACAC;YACAjD;UACA;;UAEA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UACA;YACAJ;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;UAEAiC;YACAqB;YACApD;YACAF;YACAuD;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;YACAtB;cACAuB;cAAA;cACAC;cACAvB;gBACAJ;cACA;cACAY;gBACAZ;cACA;YACA;UACA;YACAA;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA4B;MACA,IACAC,YAEAC,EAFAD;QACAE,UACAD,EADAC;MAEA;MACAC,uDACAA;QACAC;MAAA,EACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACA9E;kBACAC;gBACA;kBACA;kBACA8E;oBACA;sBACA;oBACA;oBACAC;oBACAA;sBACA;wBACAC;wBACAC;wBACAC;sBACA;oBACA;kBACA;kBACA;oBAAA;kBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBACA;oBAAA;kBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBACAvC;kBACA;oBAAA;kBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAwC;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACA;UACAL;QACA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAA;UACA;YACA;UACA;QACA;MACA;IACA;IAEAM;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACA;kBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACAxC;QACAyC;QACAxC;UACA;YACA;YACAD;cACA0C;YACA;UACA;YACA;YACA1C;cACA2C;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA5C;QACA0C;MACA;IACA;IAEA;IACAG;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACA;YACAC;UACA;YACA;YACAA;UACA;QACA;MACA;MAEA;MACAA;QACA;UAAA;QAAA;QACA;UACA9C;YACA+C;YACA7C;YACAsB;UACA;UACAwB;UACA;QACA;QACA;QACA;UACAf;QACA;MACA;MAEA;QACA;QACA;MACA;;MAEA;MACAa;QAAA,uCACAb;UACAC;UACAe;QAAA;MAAA,CACA;MAEAH;QACA;QACA;UACA;YACAb;cACA;YACA;UACA;YACAA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;YACAC;YACAgB;UACA;YACAlD;cACA+C;cACA7C;YACA;YACAiD;cACA;cAEAnD;gBACA0C;cACA;YACA;UACA;YACA;YACA;YACA1C;cACA+C;cACA7C;cACAsB;YACA;YACA3B;UACA;QACA;UACA;UACA;UACAG;YACA+C;YACA7C;YACAsB;UACA;QACA;MACA;QACA;QACA;QACAxB;UACA+C;UACA7C;UACAsB;QACA;QACA3B;MACA;IACA;IAEA;IACAuD;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACA;YACAP;UACA;YACA;YACAA;UACA;QACA;MACA;MAEA;MACAA;QACA;UAAA;QAAA;QACA;UACA9C;YACA+C;YACA7C;YACAsB;UACA;UACAwB;UACA;QACA;MACA;MAEA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAM;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACAtD;UACA+C;UACA7C;UACAsB;QACA;QACA;MACA;MAEA;QACAxB;UACA+C;UACA7C;UACAsB;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACA;YACAsB;UACA;YACAA;UACA;QACA;MACA;MACA;MACAA;QACA;UAAA;QAAA;QACA;UACA9C;YACA+C;YACA7C;YACAsB;UACA;UACA+B;UACA;QACA;QACA;QACA;UACAtB;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QACAa;UAAA,uCACAb;YACAC;YACAe;UAAA;QAAA,CACA;QAEAH;UACA;UACA;YACA;cACAb;gBACA;cACA;YACA;cACAA;YACA;UACA;QACA;QAEApC;QACA;QACA;UACA;YACA;YACA;YACA;YACA;;YAEA;cACA;cACAG;gBACA+C;gBACA7C;cACA;cACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;YACA;;YAEA;YACA;cACA;cACAF;gBACA+C;gBACA7C;cACA;cACA;YACA;YAEA;cACAhD;cACAsG;cACAC;cACAvB;cACAwB;cACAR;cACAS;cACAC;cACAC;cACAC;YACA;;YAEAjE;;YAEA;YACA;cACA;gBACAG;kBACA+C;kBACA7C;kBACAsB;gBACA;gBACA2B;kBACA;kBACA;kBAEAnD;oBACA0C;kBACA;;kBAEA;gBACA;cACA;gBACA;gBACA1C;kBACA+C;kBACA7C;kBACAsB;gBACA;cACA;YACA;cACA;cACAxB;gBACA+C;gBACA7C;gBACAsB;cACA;cACA3B;YACA;UACA;YACA;YACAG;cACA+C;cACA7C;cACAsB;YACA;UACA;QACA;UACA3B;UACA;UACAG;YACA+C;YACA7C;YACAsB;UACA;QACA;MACA;IACA;IAEA;IACAuC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MAEAlE;QACA0C;QACAzC;UACAJ;QACA;QACAY;UACAZ;UACAG;YACA+C;YACA7C;YACAsB;UACA;QACA;MACA;IACA;IAEA;IACA2C;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACArE;UACA+C;UACA7C;UACAsB;QACA;QACA;MACA;MACA;IACA;IAEA;IACA8C;MACA;QACAtE;UACA+C;UACA7C;UACAsB;QACA;QACA;MACA;MACA;MACA;QACAxB;UACA+C;UACA7C;UACAsB;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACA+C;MAAA;MACA1E;MACA;MACA;MACA;;MAEA;MACA;MAEA;QAAA;QACA;UACA;YACAA;YACA;UACA;;UACA;UACA;;UAEA;UACA;YACA;UAAA,CACA;YACA;YACA;cAAAd;YAAA;UACA;QACA;MACA;QACA;QACA;UAAA;YAAAA;UAAA;QAAA;MACA;MACAc;IACA;IAGA;IACA2E;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QAEA;UACAC;UACAC;UACAC;QACA;;QAEA;QACAlG;MACA;;MAEA;MACA;IACA;IAEA;IACAmG;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBACAhF;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAmC;gBACAnC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EAEAiF;IAAA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA9E;MACAC;QACA;QACA;QACAJ;MACA;IACA;EACA;EAEA;EACAkF;IAAA;IACAlF;IACA;IACA;;IAEA;IACA;MACAsD;QACA;MACA;IACA;EACA;EAEA;EACA6B;IACA;IACA;IACA;IACA;IACA;IACA;MACArE;MACA;IACA;;IAEA;IACA;IACAX;MACAH;MACAoF;MACAA;IACA;;IAEA;IACA;IACA;MACA;IACA;EACA;EAEA;EACAC;IACA;MACAvE;MACA;IACA;EACA;EAEA;EACAwE;IACA;MACAxE;MACA;IACA;EACA;EAEAyE;AACA;AAAA,2B;;;;;;;;;;;;;AC1rCA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/price_parity.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/price_parity.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./price_parity.vue?vue&type=template&id=027e28c9&scoped=true&\"\nvar renderjs\nimport script from \"./price_parity.vue?vue&type=script&lang=js&\"\nexport * from \"./price_parity.vue?vue&type=script&lang=js&\"\nimport style0 from \"./price_parity.vue?vue&type=style&index=0&id=027e28c9&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"027e28c9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/price_parity.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./price_parity.vue?vue&type=template&id=027e28c9&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  var g2 = _vm.list.length\n  var l0 = _vm.__map(_vm.list3, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g3 = _vm.form.data.findIndex(function (e) {\n      return e.serviceId == item.id\n    })\n    var g4 = _vm.form.data.findIndex(function (e) {\n      return e.serviceId == item.id\n    })\n    return {\n      $orig: $orig,\n      g3: g3,\n      g4: g4,\n    }\n  })\n  var l1 = _vm.showTimeModal ? _vm.displayedTimeArr.slice(0, 6) : null\n  var l2 = _vm.showTimeModal ? _vm.displayedTimeArr.slice(6) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showTimeModal = true\n    }\n    _vm.e1 = function ($event, item, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item,\n        index = _temp2.index\n      var _temp, _temp2\n      return _vm.selectTime(item, index)\n    }\n    _vm.e2 = function ($event, item, index) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item = _temp4.item,\n        index = _temp4.index\n      var _temp3, _temp4\n      return _vm.selectTime(item, index + 6)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./price_parity.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./price_parity.vue?vue&type=script&lang=js&\"", "\n<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<image :src=\"serviceInfo.cover\" mode=\"scaleToFill\"></image>\n\t\t</view>\n\t\t<view class=\"content\">\n\t\t\t<view class=\"card\">\n\t\t\t\t<view class=\"top\">\n\t\t\t\t\t<view class=\"title\">{{serviceInfo.title}}</view>\n\t\t\t\t\t<view class=\"price\" v-if=\"serviceInfo.servicePriceType !=1\">￥{{serviceInfo.price}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t<view class=\"left\">已选：</view>\n\t\t\t\t\t<view class=\"right\">\n\t\t\t\t\t\t<view class=\"\" v-if=\"serviceInfo.servicePriceType == 1\">\n\t\t\t\t\t\t\t{{yikoujiaprice}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"tag\" v-for=\"(item,index) in chooseArr\" :key=\"index\">{{item.name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"chol\" v-for=\"(item,index) in list\" :key=\"item.id\">\n\t\t\t\t<view class=\"choose\">\n\t\t\t\t\t<view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n\t\t\t\t\t<view class=\"desc\">{{item.problemContent}}</view>\n\t\t\t\t\t<view class=\"cho_box\">\n\t\t\t\t\t\t<view class=\"box_item\" v-for=\"(newItem,newIndex) in item.options\" :key=\"newIndex\"\n\t\t\t\t\t\t\t:style=\"newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':''\"\n\t\t\t\t\t\t\t@click=\"chooseOne(index,newIndex,item.inputType)\">\n\t\t\t\t\t\t\t{{newItem.name}}\n\t\t\t\t\t\t\t<view class=\"ok\" :style=\"newItem.choose? '' : 'display:none;'\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"8\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"fg\"></view>\n\t\t\t</view>\n\n\t\t\t<view class=\"chol\" v-for=\"(item, index) in list2\" :key=\"item.id\">\n\t\t\t\t<view class=\"choose\">\n\t\t\t\t\t<view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n\t\t\t\t\t<view class=\"desc\">{{item.problemContent}}</view>\n\t\t\t\t\t<view class=\"input-container\" :id=\"'input-container-' + index\">\n\t\t\t\t\t\t<input type=\"text\" v-model=\"form.data[index + list.length].val\"\n\t\t\t\t\t\t\t:placeholder=\"'请输入' + item.problemDesc\" @focus=\"handleInputFocus(index)\"\n\t\t\t\t\t\t\t@blur=\"handleInputBlur\" @input=\"handleInput\" class=\"form-input\" cursor-spacing=\"10\"\n\t\t\t\t\t\t\tconfirm-type=\"done\" :adjust-position=\"false\" :auto-height=\"false\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"fg\"></view>\n\t\t\t</view>\n\n\t\t\t<view class=\"chol\" v-for=\"(item,index) in list3\" :key=\"item.id\">\n\t\t\t\t<view class=\"choose\">\n\t\t\t\t\t<view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n\t\t\t\t\t<view class=\"desc up\">{{item.problemContent}}</view>\n\t\t\t\t\t<upload @upload=\"imgUpload\" @del=\"imgUpload\"\n\t\t\t\t\t\t:imagelist=\"form.data[form.data.findIndex(e=>e.serviceId == item.id)].val\"\n\t\t\t\t\t\t:imgtype=\"form.data.findIndex(e=>e.serviceId == item.id)\" text=\"上传图片\" :imgsize=\"3\">\n\t\t\t\t\t</upload>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"fg\"></view>\n\t\t\t</view>\n\t\t\t<view style=\"height: 300rpx;\"></view>\n\t\t</view>\n\t\t<view class=\"cart-modal\" v-if=\"showOrderModal\" @click=\"closeOrderModal\">\n\t\t\t<view class=\"cart-modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-service-info\">\n\t\t\t\t\t\t<image :src=\"serviceInfo.cover\" mode=\"aspectFill\" class=\"modal-service-image\"></image>\n\t\t\t\t\t\t<view class=\"modal-service-details\">\n\t\t\t\t\t\t\t<view class=\"modal-service-title\">{{serviceInfo.title}}</view>\n\t\t\t\t\t\t\t<view class=\"modal-service-price\" v-if=\"serviceInfo.servicePriceType !=1\">\n\t\t\t\t\t\t\t\t￥{{serviceInfo.price}}\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view class=\"modal-quantity-section\">\n\t\t\t\t\t\t\t\t<view class=\"modal-quantity-title\">数量</view>\n\t\t\t\t\t\t\t\t<view class=\"modal-quantity-control\">\n\t\t\t\t\t\t\t\t\t<view class=\"quantity-btn\" @click=\"decreaseQuantity\">-</view>\n\t\t\t\t\t\t\t\t\t<view class=\"quantity-input\">{{orderQuantity}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"quantity-btn\" @click=\"increaseQuantity\">+</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view class=\"modal-urgent-section\">\n\t\t\t\t\t\t\t\t<view class=\"modal-urgent-checkbox\" @click=\"toggleUrgent\">\n\t\t\t\t\t\t\t\t\t<view class=\"checkbox-icon\" :class=\"{ 'checked': isUrgent }\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons v-if=\"isUrgent\" type=\"checkmarkempty\" size=\"12\" color=\"#fff\">\n\t\t\t\t\t\t\t\t\t\t</uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text class=\"checkbox-label\">是否加急</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeOrderModal\">\n\t\t\t\t\t\t<uni-icons type=\"clear\" size=\"24\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<scroll-view class=\"modal-scroll-content\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"modal-address-section\" @click=\"goToAddress\">\n\t\t\t\t\t\t<view class=\"modal-section-title\">\n\t\t\t\t\t\t\t<image src=\"../static/images/position.png\" mode=\"\" class=\"section-icon\"></image>\n\t\t\t\t\t\t\t<text>服务地址</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"modal-address-content\">\n\t\t\t\t\t\t\t<view class=\"address-text\">{{mrAddress.address || '请选择服务地址'}}</view>\n\t\t\t\t\t\t\t<view class=\"address-detail\" v-if=\"mrAddress.address\">{{mrAddress.userName}}\n\t\t\t\t\t\t\t\t{{mrAddress.mobile}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-icons name=\"arrow-right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"modal-time-section\" @click=\"showTimeModal = true\">\n\t\t\t\t\t\t<view class=\"modal-section-title\">\n\t\t\t\t\t\t\t<image src=\"../static/images/clock.png\" mode=\"\" class=\"section-icon\"></image>\n\t\t\t\t\t\t\t<text>预约时间</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"modal-time-content\">\n\t\t\t\t\t\t\t<text>{{conDate + (conTime ? ' ' + conTime : '')}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-icons name=\"arrow-right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"modal-notes-section\">\n\t\t\t\t\t\t<view class=\"modal-notes-title\">服务备注</view>\n\t\t\t\t\t\t<textarea v-model=\"notes\" placeholder=\"想要额外嘱咐工作人员的可以备注哦~\" class=\"modal-notes-textarea\">\n\t\t\t\t\t\t</textarea>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<view class=\"modal-footer-buttons\">\n\t\t\t\t\t\t<view class=\"modal-order-btn\" :class=\"{ 'submitting': isSubmitting }\" @click=\"confirmOrder\">\n\t\t\t\t\t\t\t{{ isSubmitting ? '提交中...' : '立即下单' }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"time-modal\" v-if=\"showTimeModal\" @click=\"closeTimeModal\">\n\t\t\t<view class=\"time-modal-content\" @click.stop>\n\t\t\t\t<view class=\"time-modal-header\">\n\t\t\t\t\t<view class=\"time-modal-title\">请选择时间</view>\n\t\t\t\t\t<view class=\"time-modal-close\" @click=\"closeTimeModal\">\n\t\t\t\t\t\t<uni-icons type=\"clear\" size=\"24\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"time-date-section\">\n\t\t\t\t\t<view class=\"time-date-item\" v-for=\"(item,index) in dateArr\" :key=\"index\"\n\t\t\t\t\t\t:class=\"{ 'active': currentDate === index }\" @click=\"selectDate(item, index)\">\n\t\t\t\t\t\t<view class=\"date-str\">{{item.str}}</view>\n\t\t\t\t\t\t<view class=\"date-num\">{{item.date}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<scroll-view class=\"time-slots-section\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"time-slots-grid\">\n\t\t\t\t\t\t<view class=\"time-slot-column\">\n\t\t\t\t\t\t\t<view class=\"time-slot-item\" v-for=\"(item, index) in displayedTimeArr.slice(0, 6)\"\n\t\t\t\t\t\t\t\t:key=\"index\" :class=\"{\n\t\t\t                       'active': currentTime === index,\n\t\t\t                       'disabled': item.disabled\n\t\t\t                     }\" @click=\"selectTime(item, index)\">\n\t\t\t\t\t\t\t\t{{item.time}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"time-slot-column\">\n\t\t\t\t\t\t\t<view class=\"time-slot-item\" v-for=\"(item, index) in displayedTimeArr.slice(6)\"\n\t\t\t\t\t\t\t\t:key=\"index\" :class=\"{\n\t\t\t                       'active': currentTime === index + 6,\n\t\t\t                       'disabled': item.disabled\n\t\t\t                     }\" @click=\"selectTime(item, index + 6)\">\n\t\t\t\t\t\t\t\t{{item.time}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\n\t\t\t\t<view class=\"time-modal-footer\">\n\t\t\t\t\t<view class=\"time-confirm-btn\" @click=\"confirmTime\">确定预约时间</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"footer\" :style=\"footerStyle\">\n\t\t<!-- \t<view class=\"footer-item footer-service\" @click=\"contactService\">\n\t\t\t\t<view class=\"footer-icon\">\n\t\t\t\t\t<uni-icons type=\"chatbubble\" size=\"20\" color=\"#666\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"footer-text\">客服</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"footer-item footer-cart\" @click=\"goToCart\">\n\t\t\t\t<view class=\"footer-icon\">\n\t\t\t\t\t<uni-icons type=\"cart\" size=\"20\" color=\"#666\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"footer-text\">购物车</view>\n\t\t\t</view> -->\n\n\t\t\t<view class=\"footer-item footer-add-cart\" :class=\"{ 'submitting': isAddingToCart }\" @click=\"confirmAddToCart\">\n\t\t\t\t{{ isAddingToCart ? '加入中...' : '加入购物车' }}\n\t\t\t</view>\n\n\t\t\t<view class=\"footer-item footer-order\" :class=\"{ 'submitting': isSubmittingOrder }\"\n\t\t\t\t@click=\"handleOrderClick\">\n\t\t\t\t{{ isSubmittingOrder ? '提交中...' : '立即下单' }}\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tid: '',\n\t\t\t\ttype: '',\n\t\t\t\tchooseArr: [],\n\t\t\t\tlist: [], // 单选多选框\n\t\t\t\tlist2: [], // 输入框\n\t\t\t\tlist3: [], // 上传图片\n\t\t\t\tserviceInfo: {},\n\t\t\t\tform: {\n\t\t\t\t\tdata: [],\n\t\t\t\t\tid: ''\n\t\t\t\t},\n\t\t\t\ttmplIds: [\n\t\t\t\t\t'',\n\t\t\t\t\t'',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tbtArr: [], // 必填项\n\t\t\t\tfocusedInputIndex: -1,\n\t\t\t\tkeyboardHeight: 0,\n\t\t\t\tyikoujiaprice: '',\n\t\t\t\twindowHeight: 0,\n\t\t\t\tisKeyboardShow: false,\n\t\t\t\tsystemInfo: {},\n\t\t\t\tscrollTimer: null,\n\t\t\t\tisAddingToCart: false, // 加入购物车提交状态\n\t\t\t\tisSubmittingOrder: false, // 立即下单提交状态\n\t\t\t\tshowOrderModal: false, // 控制下单弹窗显示\n\n\t\t\t\t// 新增：下单相关数据\n\t\t\t\torderQuantity: 1, // 下单数量\n\t\t\t\tmrAddress: {}, // 服务地址\n\t\t\t\tisAddressManuallySelected: false, // 标记用户是否手动选择了地址\n\t\t\t\tshowTimeModal: false, // 时间选择弹窗\n\t\t\t\tcurrentDate: 0, // 当前选择的日期索引\n\t\t\t\tcurrentTime: -1, // 当前选择的时间索引\n\t\t\t\tconDate: '选择可上门时间', // 显示的日期\n\t\t\t\tconTime: '', // 显示的时间\n\t\t\t\tdateArr: [], // 日期数组\n\t\t\t\t// Base time array - always contains all time slots\n\t\t\t\tbaseTimeArr: [\n\t\t\t\t\t{ disabled: false, time: '00:00-02:00', time1: '00:00:00', time2: '02:00:00' },\n\t\t\t\t\t{ disabled: false, time: '02:00-04:00', time1: '02:00:00', time2: '04:00:00' },\n\t\t\t\t\t{ disabled: false, time: '04:00-06:00', time1: '04:00:00', time2: '06:00:00' },\n\t\t\t\t\t{ disabled: false, time: '06:00-08:00', time1: '06:00:00', time2: '08:00:00' },\n\t\t\t\t\t{ disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },\n\t\t\t\t\t{ disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },\n\t\t\t\t\t{ disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },\n\t\t\t\t\t{ disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },\n\t\t\t\t\t{ disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },\n\t\t\t\t\t{ disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' },\n\t\t\t\t\t{ disabled: false, time: '20:00-22:00', time1: '20:00:00', time2: '22:00:00' },\n\t\t\t\t\t{ disabled: false, time: '22:00-24:00', time1: '22:00:00', time2: '24:00:00' }\n\t\t\t\t],\n\t\t\t\tdisplayedTimeArr: [], // Time array adjusted based on selected date (today vs future)\n\t\t\t\tweek: [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"],\n\t\t\t\tisUrgent: false, // 是否加急\n\t\t\t\tnotes: '' // 服务备注\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\tfooterStyle() {\n\t\t\t\treturn {\n\t\t\t\t\tbottom: this.isKeyboardShow ? this.keyboardHeight + 'px' : '0px'\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 计算总价\n\t\t\ttotalPrice() {\n\t\t\t\tif (this.serviceInfo.servicePriceType === 1) {\n\t\t\t\t\treturn '0.00'; // 报价类型显示0元\n\t\t\t\t}\n\t\t\t\tconst basePrice = this.serviceInfo.price || 0;\n\t\t\t\treturn (basePrice * this.orderQuantity).toFixed(2);\n\t\t\t}\n\t\t},\n\n\t\tmethods: {\n\t\t\tdingyue() {\n\t\t\t\tconsole.log('dingyue called');\n\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// icon: 'none',\n\t\t\t\t\t// title: '模板ID不足'\n\t\t\t\t\t// });\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());\n\t\t\t\tconst selectedTmplIds = shuffled.slice(0, 3);\n\t\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\ttemplateId: id,\n\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t}));\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t// Check if any of the template IDs were rejected\n\t\t\t\t\t\tconst hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');\n\t\t\t\t\t\tconst hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');\n\t\t\t\t\t\tif (hasRejection && !hasShownModal) {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\tcontent: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',\n\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\tconfirmText: '去开启',\n\t\t\t\t\t\t\t\tconfirmColor: '#007AFF',\n\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasShownSubscriptionModal', true);\n\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\twithSubscriptions: true\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t} else if (modalRes.cancel) {\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasCanceledSubscription', true);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\thandleInputFocus(index) {\n\t\t\t\tconsole.log('输入框获得焦点:', index);\n\t\t\t\tthis.focusedInputIndex = index;\n\t\t\t\tthis.isKeyboardShow = true;\n\n\t\t\t\t// 清除之前的定时器\n\t\t\t\tif (this.scrollTimer) {\n\t\t\t\t\tclearTimeout(this.scrollTimer);\n\t\t\t\t}\n\n\t\t\t\t// 多次尝试滚动，确保定位准确\n\t\t\t\tthis.scrollToInput(index);\n\n\t\t\t\tthis.scrollTimer = setTimeout(() => {\n\t\t\t\t\tthis.scrollToInput(index);\n\t\t\t\t}, 200);\n\n\t\t\t\tthis.scrollTimer = setTimeout(() => {\n\t\t\t\t\tthis.scrollToInput(index);\n\t\t\t\t}, 400);\n\n\t\t\t\tthis.scrollTimer = setTimeout(() => {\n\t\t\t\t\tthis.scrollToInput(index);\n\t\t\t\t}, 600);\n\t\t\t},\n\n\t\t\thandleInputBlur() {\n\t\t\t\tconsole.log('输入框失去焦点');\n\t\t\t\tthis.focusedInputIndex = -1;\n\t\t\t\tthis.isKeyboardShow = false;\n\t\t\t\tthis.keyboardHeight = 0;\n\n\t\t\t\t// 清除定时器\n\t\t\t\tif (this.scrollTimer) {\n\t\t\t\t\tclearTimeout(this.scrollTimer);\n\t\t\t\t\tthis.scrollTimer = null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\thandleInput(e) {\n\t\t\t\tconsole.log('输入内容:', e.detail.value);\n\t\t\t},\n\n\t\t\tscrollToInput(index) {\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\n\n\t\t\t\t// 同时获取输入框和页面信息\n\t\t\t\tquery.select(`#input-container-${index}`).boundingClientRect();\n\t\t\t\tquery.selectViewport().scrollOffset();\n\n\t\t\t\tquery.exec((res) => {\n\t\t\t\t\tif (res && res[0] && res[1]) {\n\t\t\t\t\t\tconst inputRect = res[0];\n\t\t\t\t\t\tconst pageScrollInfo = res[1];\n\n\t\t\t\t\t\tconsole.log('输入框位置信息:', {\n\t\t\t\t\t\t\tinputRect,\n\t\t\t\t\t\t\tpageScrollInfo,\n\t\t\t\t\t\t\tsystemInfo: this.systemInfo\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 输入框距离页面顶部的绝对位置\n\t\t\t\t\t\tconst inputAbsoluteTop = inputRect.top + pageScrollInfo.scrollTop;\n\n\t\t\t\t\t\t// 获取当前系统信息\n\t\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\t\tconst windowHeight = systemInfo.windowHeight;\n\t\t\t\t\t\tconst statusBarHeight = systemInfo.statusBarHeight || 0;\n\n\t\t\t\t\t\t// 预估键盘高度（一般占屏幕高度的40-50%）\n\t\t\t\t\t\tlet keyboardHeight = this.keyboardHeight;\n\t\t\t\t\t\tif (!keyboardHeight || keyboardHeight < 100) {\n\t\t\t\t\t\t\tkeyboardHeight = windowHeight * 0.45; // 预估键盘高度\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 计算可视区域高度（窗口高度 - 键盘高度）\n\t\t\t\t\t\tconst visibleHeight = windowHeight - keyboardHeight;\n\n\t\t\t\t\t\t// 计算安全位置：让输入框显示在可视区域的上1/3处\n\t\t\t\t\t\tconst safePosition = visibleHeight * 0.3;\n\n\t\t\t\t\t\t// 计算目标滚动位置\n\t\t\t\t\t\tconst targetScrollTop = inputAbsoluteTop - safePosition - statusBarHeight;\n\n\t\t\t\t\t\tconsole.log('滚动计算详情:', {\n\t\t\t\t\t\t\tinputAbsoluteTop,\n\t\t\t\t\t\t\twindowHeight,\n\t\t\t\t\t\t\tkeyboardHeight,\n\t\t\t\t\t\t\tvisibleHeight,\n\t\t\t\t\t\t\tsafePosition,\n\t\t\t\t\t\t\ttargetScrollTop,\n\t\t\t\t\t\t\tcurrentScrollTop: pageScrollInfo.scrollTop\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 只有当需要滚动的距离超过50px时才执行滚动\n\t\t\t\t\t\tif (targetScrollTop > 0 && Math.abs(targetScrollTop - pageScrollInfo.scrollTop) > 50) {\n\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\tscrollTop: Math.max(0, targetScrollTop), // 确保不会滚动到负数位置\n\t\t\t\t\t\t\t\tduration: 300,\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tconsole.log('页面滚动成功到:', targetScrollTop);\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tconsole.error('页面滚动失败:', err);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log('无需滚动或滚动距离太小');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('获取元素位置信息失败:', res);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\timgUpload(e) {\n\t\t\t\tlet {\n\t\t\t\t\timagelist,\n\t\t\t\t\timgtype\n\t\t\t\t} = e;\n\t\t\t\tlet newFormData = [...this.form.data];\n\t\t\t\tnewFormData[imgtype] = {\n\t\t\t\t\t...newFormData[imgtype],\n\t\t\t\t\tval: [...imagelist]\n\t\t\t\t};\n\t\t\t\tthis.$set(this.form, 'data', newFormData);\n\t\t\t},\n\n\t\t\tasync getpzinfo() {\n\t\t\t\tawait this.$api.service.getPz({\n\t\t\t\t\tid: this.id,\n\t\t\t\t\ttype: this.type\n\t\t\t\t}).then(ress => {\n\t\t\t\t\tlet res = ress.data\n\t\t\t\t\tres.forEach(item => {\n\t\t\t\t\t\tif (item.isRequired == 1) {\n\t\t\t\t\t\t\tthis.btArr.push(item.id)\n\t\t\t\t\t\t}\n\t\t\t\t\t\titem.options = JSON.parse(item.options)\n\t\t\t\t\t\titem.options = item.options.map(e => {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tserviceId: item.id,\n\t\t\t\t\t\t\t\tname: e,\n\t\t\t\t\t\t\t\tchoose: false\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\tthis.list = res.filter(item => item.inputType == 3 || item.inputType == 4)\n\t\t\t\t\tthis.list.forEach((newItem, newIndex) => {\n\t\t\t\t\t\tthis.form.data.push({\n\t\t\t\t\t\t\t\"serviceId\": newItem.id,\n\t\t\t\t\t\t\t\"settingId\": this.id,\n\t\t\t\t\t\t\t\"val\": []\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\tthis.list2 = res.filter(item => item.inputType == 1)\n\t\t\t\t\tthis.list2.forEach((newItem, newindex) => {\n\t\t\t\t\t\tthis.form.data.push({\n\t\t\t\t\t\t\t\"serviceId\": newItem.id,\n\t\t\t\t\t\t\t\"settingId\": this.id,\n\t\t\t\t\t\t\t\"val\": ''\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\tconsole.log(this.list2)\n\t\t\t\t\tthis.list3 = res.filter(item => item.inputType == 2)\n\t\t\t\t\tthis.list3.forEach((newItem, newindex) => {\n\t\t\t\t\t\tthis.form.data.push({\n\t\t\t\t\t\t\t\"serviceId\": newItem.id,\n\t\t\t\t\t\t\t\"settingId\": this.id,\n\t\t\t\t\t\t\t\"val\": []\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\n\t\t\tsubmit() {\n\t\t\t\tthis.showOrderModal = true;\n\t\t\t},\n\n\t\t\tchooseOne(i, j, inputType) {\n\t\t\t\t// Single choice logic\n\t\t\t\tif (inputType == 3) {\n\t\t\t\t\tthis.list[i].options.forEach((item, index) => {\n\t\t\t\t\t\titem.choose = (index === j); // Only the selected one is true\n\t\t\t\t\t});\n\t\t\t\t} else if (inputType == 4) {\n\t\t\t\t\t// Multiple choice logic\n\t\t\t\t\tthis.list[i].options[j].choose = !this.list[i].options[j].choose;\n\t\t\t\t}\n\n\t\t\t\t// Update chooseArr based on current selections across all list items\n\t\t\t\tthis.chooseArr = [];\n\t\t\t\tthis.list.forEach(item => {\n\t\t\t\t\titem.options.forEach(tem => {\n\t\t\t\t\t\tif (tem.choose) {\n\t\t\t\t\t\t\tthis.chooseArr.push(tem);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tasync getInfo() {\n\t\t\t\tawait this.$api.service.getserviceInfo(this.id).then(res => {\n\t\t\t\t\tthis.serviceInfo = res.data\n\t\t\t\t\t// Only set yikoujiaprice if servicePriceType is 1, otherwise it will be calculated\n\t\t\t\t\tif (this.serviceInfo.servicePriceType === 1) {\n\t\t\t\t\t\tthis.yikoujiaprice = res.data.price;\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 联系客服\n\t\t\tcontactService() {\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: ['在线客服', '电话客服'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t\t\t\t// 在线客服 - 可以跳转到客服页面或打开客服聊天\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/pages/service/online-service'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (res.tapIndex === 1) {\n\t\t\t\t\t\t\t// 电话客服\n\t\t\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\t\t\tphoneNumber: '************' // 替换为实际客服电话\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 跳转购物车\n\t\t\tgoToCart() {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/cart/cart'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 确认加入购物车 (直接执行逻辑，不显示弹窗)\n\t\t\tconfirmAddToCart() {\n\t\t\t\t// 防止重复提交\n\t\t\t\tif (this.isAddingToCart) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 设置提交状态\n\t\t\t\tthis.isAddingToCart = true;\n\n\t\t\t\t// 先验证并处理表单数据\n\t\t\t\tlet copy_form = JSON.parse(JSON.stringify(this.form))\n\t\t\t\tthis.chooseArr.forEach(item => {\n\t\t\t\t\tconst targetIndex = copy_form.data.findIndex(e => e.serviceId === item.serviceId);\n\t\t\t\t\tif (targetIndex !== -1) {\n\t\t\t\t\t\tif (Array.isArray(copy_form.data[targetIndex].val)) {\n\t\t\t\t\t\t\tcopy_form.data[targetIndex].val.push(item.name);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// If val is not an array, initialize it as an array\n\t\t\t\t\t\t\tcopy_form.data[targetIndex].val = [item.name];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\tlet isValid = true\n\t\t\t\tcopy_form.data.forEach(item => {\n\t\t\t\t\tlet index = this.btArr.findIndex(e => e == item.serviceId)\n\t\t\t\t\tif (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '请填写完整后提交',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t})\n\t\t\t\t\t\tisValid = false\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\t// Fill empty val with \"无\"\n\t\t\t\t\tif (item.val == '' || (Array.isArray(item.val) && item.val.length === 0)) {\n\t\t\t\t\t\titem.val = \"无\"\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\tif (!isValid) {\n\t\t\t\t\tthis.isAddingToCart = false; // 验证失败时重置状态\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 处理数据格式\n\t\t\t\tcopy_form.data = copy_form.data.map(item => ({\n\t\t\t\t\t...item,\n\t\t\t\t\tserviceId: item.settingId,\n\t\t\t\t\tsettingId: item.serviceId\n\t\t\t\t}))\n\n\t\t\t\tcopy_form.data.forEach(item => {\n\t\t\t\t\tlet type = typeof item.val\n\t\t\t\t\tif (type != 'string') {\n\t\t\t\t\t\tif (Array.isArray(item.val) && item.val.length > 0 && typeof item.val[0] != 'string') {\n\t\t\t\t\t\t\titem.val = item.val.map(e => {\n\t\t\t\t\t\t\t\treturn e.path\n\t\t\t\t\t\t\t}).join(',')\n\t\t\t\t\t\t} else if (Array.isArray(item.val)) {\n\t\t\t\t\t\t\titem.val = item.val.join(',')\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\t// First step: Submit configuration information\n\t\t\t\tthis.$api.service.postPz(copy_form).then(res => {\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t// Second step: Add to cart after configuration is successfully submitted\n\t\t\t\t\t\tthis.$api.service.addtocar({\n\t\t\t\t\t\t\tserviceId: this.id,\n\t\t\t\t\t\t\tnum: this.orderQuantity // Use quantity from the modal\n\t\t\t\t\t\t}).then(cartRes => {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\ttitle: '加入成功'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.isAddingToCart = false;\n\n\t\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\t   url: '../pages/order'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t}).catch(cartErr => {\n\t\t\t\t\t\t\t// Failed to add to cart\n\t\t\t\t\t\t\tthis.isAddingToCart = false;\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\t\ttitle: '加入购物车失败，请重试',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tconsole.error('Add to cart failed:', cartErr);\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Configuration submission failed\n\t\t\t\t\t\tthis.isAddingToCart = false;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\ttitle: '请重新尝试',\n\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\t// Configuration submission network error\n\t\t\t\t\tthis.isAddingToCart = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Submit config failed:', err);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 关闭下单弹窗\n\t\t\tcloseOrderModal() {\n\t\t\t\tthis.showOrderModal = false;\n\t\t\t},\n\n\t\t\t// 处理立即下单点击事件 - 先验证表单完整性\n\t\t\thandleOrderClick() {\n\t\t\t\t// 防止重复提交\n\t\t\t\tif (this.isSubmittingOrder) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 验证表单完整性（类似加入购物车的验证逻辑）\n\t\t\t\tlet copy_form = JSON.parse(JSON.stringify(this.form))\n\t\t\t\tthis.chooseArr.forEach(item => {\n\t\t\t\t\tconst targetIndex = copy_form.data.findIndex(e => e.serviceId === item.serviceId);\n\t\t\t\t\tif (targetIndex !== -1) {\n\t\t\t\t\t\tif (Array.isArray(copy_form.data[targetIndex].val)) {\n\t\t\t\t\t\t\tcopy_form.data[targetIndex].val.push(item.name);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// If val is not an array, initialize it as an array\n\t\t\t\t\t\t\tcopy_form.data[targetIndex].val = [item.name];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\tlet isValid = true\n\t\t\t\tcopy_form.data.forEach(item => {\n\t\t\t\t\tlet index = this.btArr.findIndex(e => e == item.serviceId)\n\t\t\t\t\tif (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '请填写完整后提交',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t})\n\t\t\t\t\t\tisValid = false\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\tif (!isValid) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 验证通过，显示下单弹窗\n\t\t\t\tthis.showOrderModal = true;\n\t\t\t},\n\n\t\t\t// 确认下单\n\t\t\tconfirmOrder() {\n\t\t\t\t// 防止重复提交\n\t\t\t\tif (this.isSubmittingOrder) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 验证必填项\n\t\t\t\tif (this.conDate === '选择可上门时间' || !this.conTime) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择预约时间',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!this.mrAddress.id) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请先选择服务地址',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isSubmittingOrder = true; // 设置提交状态\n\n\t\t\t\tlet copy_form = JSON.parse(JSON.stringify(this.form))\n\t\t\t\tthis.chooseArr.forEach(item => {\n\t\t\t\t\tconst targetIndex = copy_form.data.findIndex(e => e.serviceId === item.serviceId);\n\t\t\t\t\tif (targetIndex !== -1) {\n\t\t\t\t\t\tif (Array.isArray(copy_form.data[targetIndex].val)) {\n\t\t\t\t\t\t\tcopy_form.data[targetIndex].val.push(item.name);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcopy_form.data[targetIndex].val = [item.name];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tlet open = true\n\t\t\t\tcopy_form.data.forEach(item => {\n\t\t\t\t\tlet index = this.btArr.findIndex(e => e == item.serviceId)\n\t\t\t\t\tif (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '请填写完整后提交',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t})\n\t\t\t\t\t\topen = false\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\t// Fill empty val with \"无\"\n\t\t\t\t\tif (item.val == '' || (Array.isArray(item.val) && item.val.length === 0)) {\n\t\t\t\t\t\titem.val = \"无\"\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\tif (!open) {\n\t\t\t\t\tthis.isSubmittingOrder = false; // 验证失败时重置状态\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (open) {\n\t\t\t\t\tcopy_form.data = copy_form.data.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tserviceId: item.settingId,\n\t\t\t\t\t\tsettingId: item.serviceId\n\t\t\t\t\t}))\n\n\t\t\t\t\tcopy_form.data.forEach(item => {\n\t\t\t\t\t\tlet type = typeof item.val\n\t\t\t\t\t\tif (type != 'string') {\n\t\t\t\t\t\t\tif (Array.isArray(item.val) && item.val.length > 0 && typeof item.val[0] != 'string') {\n\t\t\t\t\t\t\t\titem.val = item.val.map(e => {\n\t\t\t\t\t\t\t\t\treturn e.path\n\t\t\t\t\t\t\t\t}).join(',')\n\t\t\t\t\t\t\t} else if (Array.isArray(item.val)) {\n\t\t\t\t\t\t\t\titem.val = item.val.join(',')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tconsole.log(copy_form)\n\t\t\t\t\t// First step: Submit configuration information\n\t\t\t\t\tthis.$api.service.postPz(copy_form).then(res => {\n\t\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t\t// Second step: After configuration is successful, submit the order\n\t\t\t\t\t\t\tconst urgentStatus = this.isUrgent ? 1 : 0;\n\t\t\t\t\t\t\tconst selectedDateObj = this.dateArr[this.currentDate];\n\t\t\t\t\t\t\tconst selectedTimeObj = this.displayedTimeArr[this.currentTime]; // Use displayedTimeArr here\n\n\t\t\t\t\t\t\tif (!selectedDateObj || !selectedTimeObj) {\n\t\t\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: '请选择正确的日期和时间',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// Construct full date-time string\n\t\t\t\t\t\t\tlet dateStr = selectedDateObj.fullDate;\n\t\t\t\t\t\t\tlet startTimeStr = `${dateStr} ${selectedTimeObj.time1}`;\n\t\t\t\t\t\t\tlet endTimeStr = `${dateStr} ${selectedTimeObj.time2}`;\n\n\t\t\t\t\t\t\t// Convert to timestamp\n\t\t\t\t\t\t\tlet startTimestamp = new Date(startTimeStr).getTime() / 1000;\n\t\t\t\t\t\t\tlet endTimestamp = new Date(endTimeStr).getTime() / 1000;\n\n\t\t\t\t\t\t\t// Validate timestamps\n\t\t\t\t\t\t\tif (isNaN(startTimestamp) || isNaN(endTimestamp)) {\n\t\t\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: '时间格式错误，请重新选择',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tlet subForm = {\n\t\t\t\t\t\t\t\ttype: this.type,\n\t\t\t\t\t\t\t\tpid: uni.getStorageSync('pid') || 0,\n\t\t\t\t\t\t\t\taddressId: this.mrAddress.id,\n\t\t\t\t\t\t\t\tserviceId: this.id,\n\t\t\t\t\t\t\t\turgent: urgentStatus,\n\t\t\t\t\t\t\t\tnum: this.orderQuantity,\n\t\t\t\t\t\t\t\tstartTime: startTimestamp,\n\t\t\t\t\t\t\t\tendTime: endTimestamp,\n\t\t\t\t\t\t\t\ttext: this.notes,\n\t\t\t\t\t\t\t\tcouponId: '', // Temporarily no coupon support\n\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\tconsole.log('提交订单数据:', subForm);\n\n\t\t\t\t\t\t\t// Submit order\n\t\t\t\t\t\t\tthis.$api.service.subOrder(subForm).then(orderRes => {\n\t\t\t\t\t\t\t\tif (orderRes.code === '200') {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\ttitle: '下单成功',\n\t\t\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\t\t\t\t\tthis.showOrderModal = false;\n\n\t\t\t\t\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\t\t\t\t   url: '/user/wait_price'\n\t\t\t\t\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t\t\t\t// No redirection after success as per user requirement\n\t\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\ttitle: orderRes.msg || '下单失败，请重试',\n\t\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(orderErr => {\n\t\t\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\t\t\ttitle: '下单失败，请重试',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tconsole.error('Submit order failed:', orderErr);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\t\ttitle: '请重新尝试',\n\t\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tconsole.error('提交配置失败:', err);\n\t\t\t\t\t\tthis.isSubmittingOrder = false;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 数量增加\n\t\t\tincreaseQuantity() {\n\t\t\t\tthis.orderQuantity++;\n\t\t\t},\n\n\t\t\t// 数量减少\n\t\t\tdecreaseQuantity() {\n\t\t\t\tif (this.orderQuantity > 1) {\n\t\t\t\t\tthis.orderQuantity--;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 切换加急状态\n\t\t\ttoggleUrgent() {\n\t\t\t\tthis.isUrgent = !this.isUrgent;\n\t\t\t},\n\n\t\t\t// 跳转到地址选择页面\n\t\t\tgoToAddress() {\n\t\t\t\tif (this.isSubmittingOrder) return;\n\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '../user/address',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('Navigation to address page successful');\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('Navigation failed:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '导航失败，请检查页面路径',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 关闭时间选择弹窗\n\t\t\tcloseTimeModal() {\n\t\t\t\tthis.showTimeModal = false;\n\t\t\t},\n\n\t\t\t// 选择日期\n\t\t\tselectDate(item, index) {\n\t\t\t\tthis.currentDate = index;\n\t\t\t\tthis.currentTime = -1; // 重置时间选择\n\t\t\t\tthis.conTime = ''; // 重置时间显示\n\t\t\t\tthis.updateTimeAvailability(index);\n\t\t\t},\n\n\t\t\t// 选择时间\n\t\t\tselectTime(item, index) {\n\t\t\t\tif (!item || !item.time || item.disabled) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '该时间段不可选择',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.currentTime = index;\n\t\t\t},\n\n\t\t\t// 确认时间选择\n\t\t\tconfirmTime() {\n\t\t\t\tif (this.currentTime === -1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择预约时间',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst selectedTime = this.displayedTimeArr[this.currentTime]; // Use displayedTimeArr here\n\t\t\t\tif (selectedTime.disabled) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '该时间段不可用',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.conDate = this.dateArr[this.currentDate].date + '(' + this.dateArr[this.currentDate].str + ')';\n\t\t\t\tthis.conTime = selectedTime.time;\n\t\t\t\tthis.showTimeModal = false;\n\t\t\t},\n\n\t\t\t// 更新时间段可用性\n\t\t\tupdateTimeAvailability(dateIndex) {\n\t\t\t\tconsole.log('Updating time availability for dateIndex:', dateIndex);\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst currentHour = now.getHours();\n\t\t\t\tconst currentMinute = now.getMinutes();\n\n\t\t\t\t// Reset displayed time array\n\t\t\t\tthis.displayedTimeArr = [];\n\n\t\t\t\tif (dateIndex === 0) { // Today\n\t\t\t\t\tthis.baseTimeArr.forEach(item => {\n\t\t\t\t\t\tif (!item.time1) {\n\t\t\t\t\t\t\tconsole.warn(`Invalid time slot at index ${index}:`, item);\n\t\t\t\t\t\t\treturn; // Skip invalid items\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst timeStartHour = parseInt(item.time1.split(':')[0]);\n\t\t\t\t\t\tconst timeStartMinute = parseInt(item.time1.split(':')[1]);\n\n\t\t\t\t\t\t// Check if the time slot has already passed\n\t\t\t\t\t\tif (currentHour > timeStartHour || (currentHour === timeStartHour && currentMinute >= timeStartMinute)) {\n\t\t\t\t\t\t\t// Past time, do not add to displayedTimeArr\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Future time, add to displayedTimeArr and ensure it's not disabled\n\t\t\t\t\t\t\tthis.displayedTimeArr.push({ ...item, disabled: false });\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// Other dates, all time slots are available\n\t\t\t\t\tthis.displayedTimeArr = this.baseTimeArr.map(item => ({ ...item, disabled: false }));\n\t\t\t\t}\n\t\t\t\tconsole.log('Updated displayedTimeArr:', this.displayedTimeArr);\n\t\t\t},\n\n\n\t\t\t// 生成日期数组\n\t\t\tgenerateDateArray() {\n\t\t\t\tconst now = new Date();\n\t\t\t\tlet currentDate = new Date(now);\n\n\t\t\t\tfor (let i = 0; i < 4; i++) {\n\t\t\t\t\tconst month = this.addLeadingZero(currentDate.getMonth() + 1);\n\t\t\t\t\tconst date = this.addLeadingZero(currentDate.getDate());\n\t\t\t\t\tconst day = currentDate.getDay();\n\t\t\t\t\tconst year = currentDate.getFullYear();\n\n\t\t\t\t\tthis.dateArr.push({\n\t\t\t\t\t\tstr: i === 0 ? '今天' : this.week[day],\n\t\t\t\t\t\tdate: month + '-' + date,\n\t\t\t\t\t\tfullDate: `${year}-${month}-${date}` // 添加完整日期格式\n\t\t\t\t\t});\n\n\t\t\t\t\t// Move to the next day\n\t\t\t\t\tcurrentDate.setDate(currentDate.getDate() + 1);\n\t\t\t\t}\n\n\t\t\t\t// Initialize today's time availability\n\t\t\t\tthis.updateTimeAvailability(0);\n\t\t\t},\n\n\t\t\t// 添加前导零\n\t\t\taddLeadingZero(number) {\n\t\t\t\treturn number < 10 ? '0' + number : number;\n\t\t\t},\n\n\t\t\t// 获取默认地址\n\t\t\tasync getDefaultAddress() {\n\t\t\t\t// 如果用户已经手动选择了地址，不要覆盖\n\t\t\t\tif (this.isAddressManuallySelected) {\n\t\t\t\t\tconsole.log('用户已手动选择地址，跳过获取默认地址');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tlet res = await this.$api.service.getaddressDefault();\n\t\t\t\t\tconsole.log('获取默认地址:', res.data);\n\t\t\t\t\tthis.mrAddress = res.data;\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('Get default address failed:', err);\n\t\t\t\t\t// 如果获取默认地址失败，设置空对象避免显示错误\n\t\t\t\t\tthis.mrAddress = {};\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tonLoad(options) {\n\t\t\tthis.id = options.id\n\t\t\tthis.type = options.type\n\t\t\tthis.form.id = options.id\n\t\t\tthis.getInfo()\n\t\t\tthis.getpzinfo()\n\n\t\t\t// Initialize order related data\n\t\t\tthis.generateDateArray()\n\t\t\tthis.getDefaultAddress()\n\n\t\t\t// Get system info\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.systemInfo = res;\n\t\t\t\t\tthis.windowHeight = res.windowHeight;\n\t\t\t\t\tconsole.log('获取到系统信息:', res);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// Listen for keyboard height changes\n\t\tonKeyboardHeightChange(res) {\n\t\t\tconsole.log('键盘高度变化事件:', res);\n\t\t\tthis.keyboardHeight = res.height;\n\t\t\tthis.isKeyboardShow = res.height > 0;\n\n\t\t\t// When keyboard height changes, if there's a focused input, re-adjust position\n\t\t\tif (this.focusedInputIndex >= 0) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.scrollToInput(this.focusedInputIndex);\n\t\t\t\t}, 50);\n\t\t\t}\n\t\t},\n\n\t\t// Reset status when page shows\n\t\tonShow() {\n\t\t\tthis.focusedInputIndex = -1;\n\t\t\tthis.isKeyboardShow = false;\n\t\t\tthis.keyboardHeight = 0;\n\t\t\tthis.isAddingToCart = false; // Reset add to cart status\n\t\t\tthis.isSubmittingOrder = false; // Reset order submission status\n\t\t\tif (this.scrollTimer) {\n\t\t\t\tclearTimeout(this.scrollTimer);\n\t\t\t\tthis.scrollTimer = null;\n\t\t\t}\n\n\t\t\t// Listen for address selection callback\n\t\t\tlet that = this;\n\t\t\tuni.$once('chooseAddress', function(e) {\n\t\t\t\tconsole.log('收到地址选择事件:', e);\n\t\t\t\tthat.mrAddress = e;\n\t\t\t\tthat.isAddressManuallySelected = true; // 标记用户手动选择了地址\n\t\t\t});\n\n\t\t\t// Only re-fetch default address if user hasn't manually selected an address\n\t\t\t// This prevents overriding user's address selection\n\t\t\tif (!this.isAddressManuallySelected && (!this.mrAddress || !this.mrAddress.id)) {\n\t\t\t\tthis.getDefaultAddress();\n\t\t\t}\n\t\t},\n\n\t\t// Clean up when page hides\n\t\tonHide() {\n\t\t\tif (this.scrollTimer) {\n\t\t\t\tclearTimeout(this.scrollTimer);\n\t\t\t\tthis.scrollTimer = null;\n\t\t\t}\n\t\t},\n\n\t\t// Clean up when page unloads\n\t\tonUnload() {\n\t\t\tif (this.scrollTimer) {\n\t\t\t\tclearTimeout(this.scrollTimer);\n\t\t\t\tthis.scrollTimer = null;\n\t\t\t}\n\t\t},\n\n\t\twatch: {}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tmin-height: 100vh;\n\t\tposition: relative;\n\t\tpadding-bottom: 200rpx;\n\t}\n\n\t.header {\n\t\twidth: 750rpx;\n\t\theight: 376rpx;\n\t\tposition: absolute;\n\t\ttop: -300rpx;\n\t\tleft: 0;\n\t\tz-index: -999;\n\t}\n\n\t.header image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.content {\n\t\tmargin-top: 280rpx;\n\t}\n\n\t.card {\n\t\tmargin-left: 32rpx;\n\t\twidth: 686rpx;\n\t\tbackground: #FFFFFF;\n\t\tbox-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);\n\t\tborder-radius: 16rpx;\n\t\tpadding: 40rpx;\n\t}\n\n\t.card .top {\n\t\tpadding-bottom: 40rpx;\n\t\tborder-bottom: 2rpx solid #F2F3F6;\n\t}\n\n\t.card .top .title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #171717;\n\t\tletter-spacing: 2rpx;\n\t}\n\n\t.card .top .price {\n\t\tmargin-top: 12rpx;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #E72427;\n\t}\n\n\t.card .bottom {\n\t\tpadding-top: 24rpx;\n\t\tdisplay: flex;\n\t}\n\n\t.card .bottom .left {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #999999;\n\t\tpadding-top: 10rpx;\n\t}\n\n\t.card .bottom .right {\n\t\tflex: 1;\n\t\tmargin-left: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t}\n\n\t.card .bottom .right .tag {\n\t\twidth: fit-content;\n\t\theight: 44rpx;\n\t\tpadding: 0 12rpx;\n\t\tbackground: #DCEAFF;\n\t\tborder-radius: 4rpx;\n\t\tfont-size: 16rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #2E80FE;\n\t\tline-height: 44rpx;\n\t\ttext-align: center;\n\t\tmargin: 10rpx;\n\t}\n\n\t.chol .choose {\n\t\tpadding: 40rpx 32rpx;\n\t}\n\n\t.chol .choose .title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333333;\n\t}\n\n\t.chol .choose .title span {\n\t\tcolor: #E72427;\n\t}\n\n\t.chol .choose .input-container {\n\t\tmargin-top: 40rpx;\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tmin-height: 88rpx;\n\t}\n\n\t.chol .choose .form-input {\n\t\tbox-sizing: border-box;\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: #F7F7F7;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 30rpx;\n\t\tfont-size: 28rpx;\n\t\tline-height: 88rpx;\n\t\tborder: 2rpx solid transparent;\n\t\ttransition: all 0.2s ease;\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\n\t.chol .choose .form-input:focus {\n\t\tbackground: #fff;\n\t\tborder-color: #2E80FE;\n\t\tbox-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);\n\t\toutline: none;\n\t}\n\n\t.chol .choose .desc {\n\t\tmargin-top: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #ADADAD;\n\t}\n\n\t.chol .choose .up {\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.chol .choose .cho_box {\n\t\tmargin-top: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.chol .choose .cho_box .box_item {\n\t\twidth: fit-content;\n\t\tpadding: 0 20rpx;\n\t\theight: 60rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 4rpx;\n\t\tborder: 2rpx solid #D8D8D8;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #ADADAD;\n\t\tline-height: 60rpx;\n\t\tmargin-right: 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tposition: relative;\n\t}\n\n\t.chol .choose .cho_box .box_item .ok {\n\t\twidth: 20rpx;\n\t\theight: 20rpx;\n\t\tposition: absolute;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: #2E80FE;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.chol .fg {\n\t\twidth: 750rpx;\n\t\theight: 20rpx;\n\t\tbackground: #F3F4F5;\n\t}\n\n\t.footer {\n\t\tpadding: 20rpx 32rpx;\n\t\twidth: 750rpx;\n\t\tbackground: #ffffff;\n\t\tbox-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tz-index: 999;\n\t\ttransition: bottom 0.25s ease;\n\n\t\t.footer-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\theight: 88rpx;\n\t\t\ttransition: all 0.2s ease;\n\t\t}\n\n\t\t// 客服按钮 - 小占比\n\t\t.footer-service {\n\t\t\tflex: 0 0 100rpx;\n\n\t\t\t.footer-icon {\n\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t}\n\n\t\t\t.footer-text {\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tcolor: #666;\n\t\t\t}\n\t\t}\n\n\t\t// 购物车按钮 - 小占比\n\t\t.footer-cart {\n\t\t\tflex: 0 0 100rpx;\n\n\t\t\t.footer-icon {\n\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t}\n\n\t\t\t.footer-text {\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tcolor: #666;\n\t\t\t}\n\t\t}\n\n\t\t// 加入购物车按钮 - 大占比，无填充\n\t\t.footer-add-cart {\n\t\t\tflex: 1;\n\t\t\tmargin: 0 16rpx;\n\t\t\tbackground: transparent;\n\t\t\tborder: 2rpx solid #2e80fe;\n\t\t\tborder-radius: 44rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #2e80fe;\n\t\t\ttext-align: center;\n\t\t\tline-height: 84rpx;\n\n\t\t\t&:active {\n\t\t\t\tbackground: rgba(46, 128, 254, 0.1);\n\t\t\t}\n\t\t}\n\n\t\t// 立即下单按钮 - 大占比，有填充\n\t\t.footer-order {\n\t\t\tflex: 1;\n\t\t\tbackground: #2e80fe; // 修改为蓝色背景\n\t\t\tborder-radius: 44rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #ffffff;\n\t\t\ttext-align: center;\n\t\t\tline-height: 88rpx;\n\n\t\t\t&.submitting {\n\t\t\t\tbackground: #a5c7ff; // 修改为淡蓝色\n\t\t\t\topacity: 0.7;\n\t\t\t\tpointer-events: none;\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\tbackground: #1a6bd8; // 修改为深蓝色\n\t\t\t}\n\t\t}\n\t}\n\n\t/* iOS安全区域适配 */\n\t@supports (bottom: env(safe-area-inset-bottom)) {\n\t\t.footer {\n\t\t\tpadding-bottom: calc(38rpx + env(safe-area-inset-bottom));\n\t\t}\n\t}\n\n\t/* 购物车弹窗样式 */\n\t.cart-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tz-index: 9999;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t}\n\n\t.cart-modal-content {\n\t\twidth: 100%;\n\t\tmax-height: 90vh;\n\t\t/* 增加高度 */\n\t\tbackground: #ffffff;\n\t\tborder-radius: 24rpx 24rpx 0 0;\n\t\t/*减小圆角 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tanimation: slideUp 0.3s ease-out;\n\t}\n\n\t@keyframes slideUp {\n\t\tfrom {\n\t\t\ttransform: translateY(100%);\n\t\t}\n\n\t\tto {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.modal-header {\n\t\tpadding: 24rpx 32rpx;\n\t\t/* 调整内边距 */\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\t/* 调整对齐方式 */\n\t\tjustify-content: space-between;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t/* 更细的边框 */\n\t\tposition: relative;\n\t\t/* 添加相对定位 */\n\t}\n\n\t.modal-service-info {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\t/* 调整对齐方式 */\n\t\twidth: 100%;\n\t\tpadding: 16rpx 0;\n\t\tflex-wrap: wrap;\n\t\t/* 允许换行 */\n\t}\n\n\t.modal-service-image {\n\t\twidth: 120rpx;\n\t\t/* 增大图片尺寸 */\n\t\theight: 120rpx;\n\t\tborder-radius: 8rpx;\n\t\tmargin-right: 24rpx;\n\t\tflex-shrink: 0;\n\t\t/* 防止图片缩小 */\n\t}\n\n\t.modal-service-details {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.modal-service-title {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t\tmargin-bottom: 12rpx;\n\t}\n\n\t.modal-service-price {\n\t\tfont-size: 36rpx;\n\t\t/* 增大价格字体 */\n\t\tfont-weight: 600;\n\t\tcolor: #FF4D4F;\n\t\t/* 更鲜艳的红色 */\n\t\tmargin-bottom: 24rpx;\n\t\t/* 增加间距 */\n\t}\n\n\t.modal-close {\n\t\tposition: absolute;\n\t\ttop: 24rpx;\n\t\tright: 24rpx;\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 10;\n\t}\n\n\t.modal-scroll-content {\n\t\tflex: 1;\n\t\toverflow-y: auto;\n\t\tpadding: 0 32rpx;\n\t}\n\n\t.modal-selected-info {\n\t\tpadding: 32rpx 0;\n\t\tborder-bottom: 2rpx solid #f5f5f5;\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t}\n\n\t.modal-selected-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\tmargin-right: 20rpx;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.modal-selected-tags {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.modal-tag {\n\t\tpadding: 8rpx 16rpx;\n\t\tbackground: #DCEAFF;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #2E80FE;\n\t\tmargin: 8rpx 16rpx 8rpx 0;\n\t}\n\n\t.modal-chol {\n\t\tborder-bottom: 2rpx solid #f5f5f5;\n\t}\n\n\t.modal-choose {\n\t\tpadding: 32rpx 0;\n\t}\n\n\t.modal-choose-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.modal-choose-title span {\n\t\tcolor: #E72427;\n\t}\n\n\t.modal-choose-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.modal-cho-box {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.modal-box-item {\n\t\tpadding: 16rpx 24rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder: 2rpx solid #e5e5e5;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin: 0 16rpx 16rpx 0;\n\t\tposition: relative;\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.modal-ok {\n\t\tposition: absolute;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\twidth: 24rpx;\n\t\theight: 24rpx;\n\t\tbackground: #2E80FE;\n\t\tborder-radius: 0 8rpx 0 8rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.modal-input-container {\n\t\tmargin-top: 16rpx;\n\t}\n\n\t.modal-form-input {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder: 2rpx solid #e5e5e5;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 24rpx;\n\t\tfont-size: 28rpx;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.modal-form-input:focus {\n\t\tborder-color: #2E80FE;\n\t\tbackground: #fff;\n\t}\n\n\t.modal-footer {\n\t\tpadding: 32rpx;\n\t\tbackground: #fff;\n\t\tborder-top: 2rpx solid #f5f5f5;\n\t\tflex-shrink: 0;\n\t}\n\n\t.modal-add-cart-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: #E72427;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s ease;\n\n\t\t&.submitting {\n\t\t\tbackground: #f5a5a7;\n\t\t\topacity: 0.7;\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n\n\t.modal-add-cart-btn:active {\n\t\tbackground: #c41e20;\n\t}\n\n\t/* 新增：下单弹窗样式 */\n\t.modal-selected-section {\n\t\tpadding: 24rpx 32rpx;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t}\n\n\t.modal-quantity-section {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 16rpx 0;\n\t\t/* margin-top: 16rpx; remove this margin-top */\n\t\twidth: 100%;\n\t\t/* Ensure it takes full width within service-details */\n\t}\n\n\n\t.modal-quantity-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t}\n\n\t.modal-quantity-control {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.quantity-btn {\n\t\twidth: 56rpx;\n\t\theight: 56rpx;\n\t\tborder: 1rpx solid #e5e5e5;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tbackground: #fff;\n\t}\n\n\t.quantity-btn:active {\n\t\tbackground: #f5f5f5;\n\t}\n\n\t.quantity-input {\n\t\twidth: 80rpx;\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin: 0 20rpx;\n\t}\n\n\t.modal-address-section,\n\t.modal-time-section {\n\t\tpadding: 24rpx;\n\t\tpadding-right: 80rpx;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t}\n\n\t.modal-section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t}\n\n\t.section-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tmargin-right: 12rpx;\n\t}\n\n\t.modal-address-content,\n\t.modal-time-content {\n\t\tflex: 1;\n\t\tmargin: 0 16rpx;\n\t\ttext-align: right;\n\t}\n\n\t.address-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #2E80FE;\n\t\tmargin-bottom: 4rpx;\n\t}\n\n\t.address-detail {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.modal-time-content text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #2E80FE;\n\t}\n\n\t.modal-urgent-section {\n\t\tpadding: 16rpx 0;\n\t\t/* Adjusted padding */\n\t\tborder-bottom: none;\n\t\t/* Removed border, it's now inside modal-service-info */\n\t\twidth: 100%;\n\t\t/* Ensure it takes full width within service-details */\n\t}\n\n\t.modal-urgent-checkbox {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.checkbox-icon {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder: 2rpx solid #e5e5e5;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 16rpx;\n\t\tbackground: #fff;\n\t}\n\n\t.checkbox-icon.checked {\n\t\tbackground: #2E80FE;\n\t\tborder-color: #2E80FE;\n\t}\n\n\t.checkbox-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t.modal-notes-section {\n\t\tpadding: 32rpx 0;\n\tmargin-right: 60rpx;\n\t\t\n\t}\n\n\t.modal-notes-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.modal-notes-textarea {\n\t\twidth: 100%;\n\t\tmin-height: 160rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder: 1rpx solid #e5e5e5;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tfont-size: 28rpx;\n\t\t\n\t\tbox-sizing: border-box;\n\t}\n\n\t.modal-notes-textarea:focus {\n\t\tborder-color: #2E80FE;\n\t\tbackground: #fff;\n\t}\n\n\t.modal-total-price {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #E72427;\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.modal-footer-buttons {\n\t\tdisplay: flex;\n\t\tgap: 24rpx;\n\t}\n\n\t.modal-footer-buttons .modal-add-cart-btn {\n\t\tflex: 1;\n\t\tbackground: transparent;\n\t\tborder: 2rpx solid #2E80FE;\n\t\tcolor: #2E80FE;\n\t}\n\n\t.modal-footer-buttons .modal-add-cart-btn:active {\n\t\tbackground: rgba(46, 128, 254, 0.1);\n\t}\n\n\t.modal-order-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: #FF4D4F;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s ease;\n\n\t\t&.submitting {\n\t\t\tbackground: #f5a5a7;\n\t\t\topacity: 0.7;\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n\n\t.modal-order-btn:active {\n\t\tbackground: #c41e20;\n\t}\n\n\t/* 时间选择弹窗样式 */\n\t.time-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tz-index: 9999;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t}\n\n\t.time-modal-content {\n\t\twidth: 100%;\n\t\tmax-height: 80vh;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 32rpx 32rpx 0 0;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tanimation: slideUp 0.3s ease-out;\n\t}\n\n\t.time-modal-header {\n\t\tpadding: 32rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tborder-bottom: 2rpx solid #f5f5f5;\n\t\tflex-shrink: 0;\n\t}\n\n\t.time-modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t.time-modal-close {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.time-date-section {\n\t\tpadding: 32rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\t\tborder-bottom: 2rpx solid #f5f5f5;\n\t}\n\n\t.time-date-item {\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tpadding: 16rpx 24rpx;\n\t\tborder-radius: 12rpx;\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.time-date-item.active {\n\t\tcolor: #2E80FE;\n\t\tbackground: rgba(46, 128, 254, 0.1);\n\t}\n\n\t.time-date-item.active .date-str {\n\t\tcolor: #2E80FE;\n\t}\n\n\t.date-str {\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.date-num {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.time-date-item.active .date-num {\n\t\tcolor: #2E80FE;\n\t}\n\n\t.time-slots-section {\n\t\tflex: 1;\n\t\tpadding: 32rpx;\n\t\tmax-height: 400rpx;\n\t}\n\n\t.time-slots-grid {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tgap: 24rpx;\n\t}\n\n\t.time-slot-column {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 16rpx;\n\t}\n\n\t.time-slot-item {\n\t\theight: 80rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder: 2rpx solid #e5e5e5;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\tline-height: 76rpx;\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.time-slot-item.active {\n\t\tbackground: #2E80FE;\n\t\tborder-color: #2E80FE;\n\t\tcolor: #fff;\n\t}\n\n\t.time-slot-item.disabled {\n\t\tbackground: #f0f0f0;\n\t\tborder-color: #e0e0e0;\n\t\tcolor: #ccc;\n\t\tpointer-events: none;\n\t}\n\n\t.time-modal-footer {\n\t\tpadding: 32rpx;\n\t\tborder-top: 2rpx solid #f5f5f5;\n\t\tflex-shrink: 0;\n\t}\n\n\t.time-confirm-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: #2E80FE;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.time-confirm-btn:active {\n\t\tbackground: #1a6bd8;\n\t}\n\n\t/* 添加新增样式 */\n\t.modal-header-tip {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\twidth: 100%;\n\t\tpadding: 16rpx 48rpx;\n\t}\n\n\t.modal-selected-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t.modal-specs-section {\n\t\tpadding: 24rpx 32rpx;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t}\n\n\t.modal-specs-tags {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tmargin-top: 16rpx;\n\t}\n\n\t.modal-spec-tag {\n\t\tpadding: 8rpx 20rpx;\n\t\tbackground: #f0f9ff;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #2E80FE;\n\t\tmargin: 8rpx 16rpx 8rpx 0;\n\t}\n\n\t.modal-notes-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 16rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.notes-limit {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tfont-weight: normal;\n\t}\n\n\t.textarea-counter {\n\t\ttext-align: right;\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-top: 8rpx;\n\t}\n\n\t.upload-photo-btn {\n\t\twidth: 160rpx;\n\t\theight: 160rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder: 1rpx dashed #ddd;\n\t\tborder-radius: 8rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 24rpx;\n\t}\n\n\t.upload-photo-btn text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tmargin-top: 8rpx;\n\t}\n\n\t.modal-notes-section {\n\t\tpadding: 24rpx 32rpx;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./price_parity.vue?vue&type=style&index=0&id=027e28c9&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./price_parity.vue?vue&type=style&index=0&id=027e28c9&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754788689568\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}