<view class="pages-mine"><view class="header"><view class="header-content"><view class="avatar_view"><image class="avatar" mode="aspectFill" src="{{userInfo.avatarUrl}}"></image></view><view class="user-info"><block wx:if="{{!isLoggedIn}}"><view><button class="{{[(isLoading)?'loading':'']}}" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['showLoginPopup',['$event']]]]]}}" bindtap="__e">{{''+(isLoading?'登录中...':'用户登录')+''}}</button></view></block><block wx:else><view class="user-info-logged"><view class="nickname">{{userInfo.nickName}}</view><block wx:if="{{userInfo.phone}}"><view class="phone-number">{{userInfo.phone}}</view></block><block wx:else><view class="bind-phone-container"><button class="bind-phone-btn" disabled="{{isBindingPhone}}" data-event-opts="{{[['tap',[['showBindPhonePopup',['$event']]]]]}}" bindtap="__e">{{''+(isBindingPhone?'绑定中...':'绑定手机号')+''}}</button></view></block></view></block></view><view data-event-opts="{{[['tap',[['navigateTo',['../user/userProfile']]]]]}}" class="settings" bindtap="__e"><view class="iconfont icon-xitong text-bold _i"></view></view></view></view><view class="mine-menu-list box-shadow fill-base box1"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">我的订单</view></view><view data-event-opts="{{[['tap',[['dingyue']]]]}}" class="flex-warp pt-lg pb-lg" bindtap="__e"><block wx:for="{{orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['orderList','',index,'url']]]]]]]}}" class="order-item" bindtap="__e"><view class="icon-container"><u-icon vue-id="{{'fe5e5c8c-1-'+index}}" name="{{item.icon}}" color="#448cfb" size="28" bind:__l="__l"></u-icon><block wx:if="{{item.count>0}}"><view class="number-circle">{{item.count}}</view></block></view><view class="mt-sm">{{item.text}}</view></view></block></view></view><view class="spacer"></view><view class="mine-tool-list fill-base"><view class="flex-warp"><block wx:for="{{toolList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['toolList','',index,'url']]]]]]]}}" class="{{['list-item',(item.text==='切换师傅版'||item.text==='邀请有礼')?'master-side':'']}}" bindtap="__e"><u-icon vue-id="{{'fe5e5c8c-2-'+index}}" name="{{item.icon}}" color="{{item.text==='切换师傅版'||item.text==='邀请有礼'?'#E41F19':'#599eff'}}" size="24" bind:__l="__l"></u-icon><view class="item-text">{{item.text}}</view></view></block></view></view><view class="floating-contact"><view class="contact-container"><u-icon vue-id="fe5e5c8c-3" name="server-man" color="#576b95" size="24" bind:__l="__l"></u-icon><button class="contact-btn" open-type="contact" bindcontact="handleContact" session-from="sessionFrom">客服</button></view></view><block wx:if="{{loginPopupVisible}}"><view data-event-opts="{{[['tap',[['hideLoginPopup',['$event']]]]]}}" class="login-popup-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="login-popup" catchtap="__e"><view data-event-opts="{{[['tap',[['hideLoginPopup',['$event']]]]]}}" class="close-btn" bindtap="__e"><view class="iconfont icon-close _i"></view></view><view class="popup-content"><view class="welcome-title">欢迎登录今师傅</view><view class="welcome-subtitle">登录后即可享受完整服务</view><view class="agreement-section"><view data-event-opts="{{[['tap',[['toggleAgreement',['$event']]]]]}}" class="checkbox-container" bindtap="__e"><view class="{{['checkbox',(agreedToTerms)?'checked':'']}}"><block wx:if="{{agreedToTerms}}"><view class="iconfont icon-check _i">✓</view></block></view><view class="agreement-text">我已阅读并同意<text data-event-opts="{{[['tap',[['navigateToAgreement',['service']]]]]}}" class="link" catchtap="__e">《今师傅服务协议》</text><text data-event-opts="{{[['tap',[['navigateToAgreement',['privacy']]]]]}}" class="link" catchtap="__e">《隐私政策》</text></view></view></view><button class="{{['phone-login-btn',(!agreedToTerms||isLoading)?'disabled':'']}}" disabled="{{!agreedToTerms||isLoading}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['onGetPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">{{''+(isLoading?'登录中...':'手机号快捷登录')+''}}</button></view></view></view></block><block wx:if="{{bindPhonePopupVisible}}"><view data-event-opts="{{[['tap',[['hideBindPhonePopup',['$event']]]]]}}" class="login-popup-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="login-popup" catchtap="__e"><view data-event-opts="{{[['tap',[['hideBindPhonePopup',['$event']]]]]}}" class="close-btn" bindtap="__e"><view class="iconfont icon-close _i"></view></view><view class="popup-content"><view class="welcome-title">绑定手机号</view><view class="welcome-subtitle">绑定手机号后可享受完整服务</view><view class="input-group"><view class="input-item"><view class="input-icon"><u-icon vue-id="fe5e5c8c-4" name="phone" color="#3b82f6" size="18" bind:__l="__l"></u-icon></view><input class="input-field" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['bindPhoneForm']]]]]}}" value="{{bindPhoneForm.phone}}" bindinput="__e"/></view><view class="input-item"><view class="input-icon"><u-icon vue-id="fe5e5c8c-5" name="chat" color="#3b82f6" size="18" bind:__l="__l"></u-icon></view><input class="input-field" type="number" placeholder="请输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['bindPhoneForm']]]]]}}" value="{{bindPhoneForm.code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendBindPhoneSmsCode',['$event']]]]]}}" class="{{['sms-btn',(bindPhoneSmsCountdown>0)?'disabled':'']}}" bindtap="__e">{{''+(bindPhoneSmsCountdown>0?bindPhoneSmsCountdown+'s':'获取验证码')+''}}</view></view></view><button class="{{['phone-login-btn',(!canBindPhone||isBindingPhone)?'disabled':'']}}" disabled="{{!canBindPhone||isBindingPhone}}" data-event-opts="{{[['tap',[['handleBindPhone',['$event']]]]]}}" bindtap="__e">{{''+(isBindingPhone?'绑定中...':'绑定手机号')+''}}</button></view></view></view></block><tabbar vue-id="fe5e5c8c-6" cur="3" bind:__l="__l"></tabbar></view>