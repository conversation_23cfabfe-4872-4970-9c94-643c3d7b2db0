{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?3a7e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?8b12", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?09a4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?6c29", "uni-app:///shifu/master_my_order.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?4254", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_my_order.vue?8995"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "limit", "coachId", "shifuId", "tmplIds", "status", "list", "name", "value", "currentIndex", "page", "orderList", "pay_typeArr", "isLoading", "showDiffApply", "currentOrderItemForDiff", "diffApplyForm", "diffAmount", "reasonType", "reasonDetail", "partsimgs", "diffApplyRules", "required", "message", "trigger", "validator", "onReady", "onReachBottom", "onPullDownRefresh", "methods", "getDiffStatusText", "showDiffApplyModal", "imgUploadDiff", "console", "imgtype", "closeDiffApplyModal", "diffApplyConfirm", "uni", "icon", "title", "partsImgsString", "orderId", "partsImgs", "res", "showDiffCancelModal", "diffItem", "content", "confirmText", "cancelText", "success", "diffCancel", "cancelId", "id", "cancellModal", "dingyue", "templateId", "templateCategoryId", "selectedTmplIds", "fail", "loadMore", "refreshList", "setTimeout", "fetchOrders", "payType", "pageNum", "pageSize", "filter", "map", "item", "orderDiffPriceList", "goDetail", "url", "goUrl", "showConfirmModal", "startFu", "queren", "updateHigh", "shiInfoid", "userId", "role", "getList", "handleHeader", "tab", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,aAAa,oTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,oTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsHl3B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC,UACA,gDACA,+CACA,+CACA,8CACA;MACAC;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QAAA;QACAC;QACAC;MACA;;MACAC;QACAJ;UACAK;UACAC;UACAC;QACA;UACAC;YACA;UACA;UACAF;UACAC;QACA;QACAL;UACAG;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAE;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QAAA;QACAd;QACAC;QAAA;QACAC;QACAC;MACA;;MACA;IACA;IAEA;IACAY;MACAC;MACA;QAAAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAlB;QACAC;QACAC;QACAC;MACA;IACA;IAEAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACAC;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAxB;kBACAC;kBAAA;kBACAC;kBACAuB;gBACA;cAAA;gBANAC;gBAOA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAL;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAI;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAK;MAAA;MACA;MACA,0FACAC,wDACA;MAEAR;QACAE;QACAO;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAjB;gBACA;gBACAkB;gBAAA;gBAAA,OACA;kBACAC;gBACA;cAAA;gBAFAT;gBAGA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAL;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAoB;MAAA;MACAhB;QACAE;QACAO;QACAC;QACAC;QACAC;UACA;YACA;cACAG;YACA;cACA;gBAAA;gBACAf;kBACAC;kBACAC;gBACA;gBACA;cACA;gBACAF;kBACAC;kBACAC;gBACA;cACA;YACA;cACAF;gBACAC;gBACAC;cACA;cACAN;YACA;UACA;QACA;MACA;IACA;IAEAqB;MAAA;MACArB;MACA;MACA;MACA;QACAA;QACA;MACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACAsB;UACAC;QACA;MAAA;MACAnB;QACAjC;QACA6C;UACAhB;UACA;UACA;UACAwB;YACAxB;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;cACA;cACA;cACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACAyB;UACAzB;QACA;MACA;IACA;IAEA0B;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACAC;QACAxB;MACA;IACA;IAEAyB;MAAA;MAAA;MACA;QACA5D;QACA6D;QACAC;QACAC;MACA;QACA;UACA5B;YACAC;YACAC;UACA;UACA;QACA;UACA;UACA,0BACA2B;YAAA;UAAA,GACAC;YAAA,uCACAC;cACAL;cACA;cACAM;YAAA;UAAA,CACA;UAEA;YACA;UACA;YACA;UACA;UAEA;UACA;QACA;QACA;QACA;MACA;QACA;QACA;QACAhC;UACAE;UACAD;QACA;QACAL;QACA;MACA;IACA;IAEAqC;MACAjC;MACAA;QACAkC;MACA;IACA;IAEAC;MACAnC;QACAkC;MACA;IACA;IAEAE;MAAA;MACApC;QACAE;QACAO;QACAC;QACAC;QACAC;UACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAyB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAtB;kBACAW;gBACA;cAAA;gBAHApB;gBAIA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAL;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA0C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAvB;kBACAW;gBACA;cAAA;gBAHApB;gBAIA;kBACAN;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAL;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA2C;MACA;MACA;QACA3C;QACA;MACA;MACA;MACA;QACA4C;MACA;QACA5C;QACA;MACA;MACA;QACA6C;QACAC;QACAhB;MACA;QACA9B;MACA;QACAA;MACA;IACA;IAEA+C;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;MACAlD;MACA;IACA;IAEA;MACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChoBA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_my_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_my_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true&\"\nvar renderjs\nimport script from \"./master_my_order.vue?vue&type=script&lang=js&\"\nexport * from \"./master_my_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b530dfe4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_my_order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=template&id=b530dfe4&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--form/u--form\" */ \"uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form-item/u-form-item\" */ \"uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--textarea/u--textarea\" */ \"uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.orderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.orderDiffPriceList && item.orderDiffPriceList.length > 0\n    var l0 = g0\n      ? _vm.__map(item.orderDiffPriceList, function (diffItem, diffIndex) {\n          var $orig = _vm.__get_orig(diffItem)\n          var m0 = _vm.getDiffStatusText(diffItem.status)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"handleHeader(item)\">\n\t\t\t\t<view :style=\"currentIndex == item.value ? 'color:#2E80FE;' : ''\">{{ item.name }}</view>\n\t\t\t\t<view class=\"blue\" :style=\"currentIndex == item.value ? '' : 'background-color:#fff;'\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view @click=\"dingyue()\" class=\"main\">\n\t\t\t<!-- 主订单 -->\n\t\t\t<view class=\"main_item\" v-for=\"(item, index) in orderList\" :key=\"index\" @click=\"goDetail(item)\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t<view class=\"type\">{{ item.payType == -1 ? '已取消' : pay_typeArr[item.payType] }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"righ\" v-if=\"item.payType == 7 || (item.payType == 7 && item.isAftermarket === 1)\">\n\t\t\t\t\t\t<view>￥{{ item.coachServicePrice }}</view>\n\t\t\t\t\t\t<view>x{{ item.num }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 3 ||item.payType === 5 \" @click.stop=\"showDiffApplyModal(item)\">\n\t\t\t\t\t 差价申请\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType >= -1 && item.payType < 7\" @click.stop=\"cancellModal(item)\">\n\t\t\t\t\t\t  取消接单\n\t\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 3\" @click.stop=\"showConfirmModal(item, 'queren')\">\n\t\t\t\t\t\t确认到达\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qzf\" v-if=\"item.payType === 5\" @click.stop=\"showConfirmModal(item, 'startFu')\">\n\t\t\t\t\t\t开始服务\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 子订单（差价申请列表） -->\n\t\t\t\t<view v-if=\"item.orderDiffPriceList && item.orderDiffPriceList.length > 0\" class=\"sub_orders\">\n\t\t\t\t\t<view class=\"sub_title\">差价申请记录</view>\n\t\t\t\t\t<view class=\"sub_item\" v-for=\"(diffItem, diffIndex) in item.orderDiffPriceList\" :key=\"diffItem.id\" @click.stop=\"\">\n\t\t\t\t\t\t<view class=\"sub_head\">\n\t\t\t\t\t\t\t<view class=\"sub_no\">差价单号：{{ diffItem.diffCode }}</view>\n\t\t\t\t\t\t\t<view class=\"sub_status\">{{ getDiffStatusText(diffItem.status) }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"sub_content\">\n\t\t\t\t\t\t\t<view class=\"sub_info\">\n\t\t\t\t\t\t\t\t<view class=\"sub_amount\">差价金额：￥{{ diffItem.diffAmount }}</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_reason\">原因：{{ diffItem.reasonDetail }}</view>\n\t\t\t\t\t\t\t\t<view class=\"sub_time\">申请时间：{{ diffItem.createdTime }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"sub_actions\">\n\t\t\t\t\t\t\t\t<view class=\"sub_qzf\" v-if=\"diffItem.status === 0\" @click.stop=\"showDiffCancelModal(item, diffItem)\">\n\t\t\t\t\t\t\t\t\t取消差价\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-loadmore :status=\"status\" @loadmore=\"loadMore\" />\n\n\t\t<!-- 自定义差价申请弹窗 -->\n\t\t<view class=\"diff-apply-modal\" v-if=\"showDiffApply\" @click=\"closeDiffApplyModal\">\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\n\t\t\t\t<!-- 弹窗头部 -->\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">差价申请</view>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeDiffApplyModal\">\n\t\t\t\t\t\t<text class=\"close-icon\">×</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 弹窗内容 -->\n\t\t\t\t<view class=\"modal-body\">\n\t\t\t\t\t<u--form\n\t\t\t\t\t\tlabelPosition=\"left\"\n\t\t\t\t\t\t:model=\"diffApplyForm\"\n\t\t\t\t\t\t:rules=\"diffApplyRules\"\n\t\t\t\t\t\tref=\"diffApplyForm\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<u-form-item label=\"差价金额\" prop=\"diffAmount\" borderBottom ref=\"item1\">\n\t\t\t\t\t\t\t<u--input v-model=\"diffApplyForm.diffAmount\" placeholder=\"请输入差价金额\" type=\"number\" border=\"none\"></u--input>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t\t<u-form-item label=\"差价原因\" borderBottom>\n\t\t\t\t\t\t\t<view class=\"reason-type-display\">\n\t\t\t\t\t\t\t\t<text class=\"reason-type-text\">配件不符合</text>\n\t\t\t\t\t\t\t\t<view class=\"reason-type-badge\">类型: 1</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t\t<u-form-item label=\"原因详情\" prop=\"reasonDetail\" borderBottom ref=\"item3\">\n\t\t\t\t\t\t\t<u--textarea v-model=\"diffApplyForm.reasonDetail\" placeholder=\"请输入差价原因详情\" count></u--textarea>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t\t<u-form-item label=\"配件图\" borderBottom ref=\"item4\">\n\t\t\t\t\t\t\t<view class=\"upload-container\">\n\t\t\t\t\t\t\t\t<upload @upload=\"imgUploadDiff\" @del=\"imgUploadDiff\" :imagelist=\"diffApplyForm.partsimgs\"\n\t\t\t\t\t\t\t\t\timgtype=\"partsimgs\" imgclass=\"parts-img\" text=\"上传配件图\" :imgsize=\"9\"></upload>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-form-item>\n\t\t\t\t\t</u--form>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 弹窗底部按钮 -->\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<view class=\"btn-cancel\" @click=\"closeDiffApplyModal\">取消</view>\n\t\t\t\t\t<view class=\"btn-confirm\" @click=\"diffApplyConfirm\">确认申请</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport Upload from '@/components/upload.vue'; // Import upload component\n\texport default {\n\t\tcomponents: {\n\t\t\tUpload,\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlimit: 10,\n\t\t\t\tcoachId: '',\n\t\t\t\tshifuId: '',\n\t\t\t\ttmplIds: [\n\t\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t\t'9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY',\n\t\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tstatus: 'loadmore',\n\t\t\t\tlist: [{\n\t\t\t\t\t\tname: '全部',\n\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '待上门',\n\t\t\t\t\t\tvalue: 3\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '待服务',\n\t\t\t\t\t\tvalue: 5\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '服务中',\n\t\t\t\t\t\tvalue: 6\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '已完成',\n\t\t\t\t\t\tvalue: 7\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '售后',\n\t\t\t\t\t\tvalue: 8\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\tcurrentIndex: 0,\n\t\t\t\tpage: 0,\n\t\t\t\torderList: [],\n\t\t\t\tpay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],\n\t\t\t\tisLoading: false, // Flag to prevent multiple API calls\n\n\t\t\t\t// For diffapply modal\n\t\t\t\tshowDiffApply: false,\n\t\t\t\tcurrentOrderItemForDiff: null, // To store the item for which diff apply is initiated\n\t\t\t\tdiffApplyForm: {\n\t\t\t\t\tdiffAmount: '',\n\t\t\t\t\treasonType: 1, // 差价原因类型，目前固定为1代表配件不符合\n\t\t\t\t\treasonDetail: '',\n\t\t\t\t\tpartsimgs: [], // 配件图片\n\t\t\t\t},\n\t\t\t\tdiffApplyRules: {\n\t\t\t\t\tdiffAmount: [{\n\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\tmessage: '请输入差价金额',\n\t\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t\t}, {\n\t\t\t\t\t\tvalidator: (rule, value, callback) => {\n\t\t\t\t\t\t\treturn value >= 0.01;\n\t\t\t\t\t\t},\n\t\t\t\t\t\tmessage: '差价金额必须大于等于0.01',\n\t\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t\t}],\n\t\t\t\t\treasonDetail: [{\n\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\tmessage: '请输入差价原因详情',\n\t\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t\t}],\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tonReady() {\n\t\t\t// We need to call this in onReady to ensure u-form is ready\n\t\t\tthis.$refs.diffApplyForm.setRules(this.diffApplyRules);\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.loadMore();\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.refreshList();\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取差价申请状态文本\n\t\t\tgetDiffStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'-1': '已取消',\n\t\t\t\t\t0: '待确认',\n\t\t\t\t\t1: '已确认待支付', \n\t\t\t\t\t2: '已支付',\n\t\t\t\t\t3: '已拒绝'\n\t\t\t\t};\n\t\t\t\treturn statusMap[status] || '未知状态';\n\t\t\t},\n\t\t\t\n\t\t\tshowDiffApplyModal(item) {\n\t\t\t\tthis.currentOrderItemForDiff = item;\n\t\t\t\tthis.diffApplyForm = { // Reset form for new application\n\t\t\t\t\tdiffAmount: '',\n\t\t\t\t\treasonType: 1, // 差价原因类型，目前固定为1代表配件不符合\n\t\t\t\t\treasonDetail: '',\n\t\t\t\t\tpartsimgs: [], // 配件图片\n\t\t\t\t};\n\t\t\t\tthis.showDiffApply = true;\n\t\t\t},\n\n\t\t\t// 处理差价申请中的图片上传\n\t\t\timgUploadDiff(e) {\n\t\t\t\tconsole.log('imgUploadDiff event:', e);\n\t\t\t\tconst { imagelist, imgtype } = e;\n\t\t\t\tthis.$set(this.diffApplyForm, imgtype, imagelist);\n\t\t\t},\n\n\t\t\t// 关闭差价申请弹窗\n\t\t\tcloseDiffApplyModal() {\n\t\t\t\tthis.showDiffApply = false;\n\t\t\t\t// 重置表单数据\n\t\t\t\tthis.diffApplyForm = {\n\t\t\t\t\tdiffAmount: '',\n\t\t\t\t\treasonType: 1,\n\t\t\t\t\treasonDetail: '',\n\t\t\t\t\tpartsimgs: [],\n\t\t\t\t};\n\t\t\t},\n\n\t\t\tasync diffApplyConfirm() {\n\t\t\t\t// Validate the form before submitting\n\t\t\t\ttry {\n\t\t\t\t\tawait this.$refs.diffApplyForm.validate();\n\t\t\t\t\t// Form is valid, proceed with API call\n\t\t\t\t\tif (!this.currentOrderItemForDiff) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '订单信息缺失'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 准备配件图片数据 - 根据API文档，partsImgs是string类型，需要转换\n\t\t\t\t\t\tconst partsImgsString = this.diffApplyForm.partsimgs.map(img => img.path).join(',');\n\n\t\t\t\t\t\tconst res = await this.$api.shifu.diffApply({\n\t\t\t\t\t\t\torderId: this.currentOrderItemForDiff.id,\n\t\t\t\t\t\t\tdiffAmount: parseFloat(this.diffApplyForm.diffAmount),\n\t\t\t\t\t\t\treasonType: this.diffApplyForm.reasonType, // 差价原因类型(1配件不符合)\n\t\t\t\t\t\t\treasonDetail: this.diffApplyForm.reasonDetail,\n\t\t\t\t\t\t\tpartsImgs: partsImgsString // 配件图片，string类型，多个图片用逗号分隔\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '差价申请成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.showDiffApply = false;\n\t\t\t\t\t\t\tthis.refreshList(); // Refresh list to reflect changes\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: res.msg || '差价申请失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.error('Error in diffApply:', err);\n\t\t\t\t\t}\n\t\t\t\t} catch (errors) {\n\t\t\t\t\tconsole.error('Form validation failed:', errors);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请检查填写信息'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tshowDiffCancelModal(item, diffItem ) {\n\t\t\t\tconst title = diffItem ? '取消差价申请' : '取消差价申请';\n\t\t\t\tconst content = diffItem ? \n\t\t\t\t\t`确定要取消差价单号 ${diffItem.diffCode} 的申请吗？` : \n\t\t\t\t\t'确定要取消此订单的差价申请吗？';\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: title,\n\t\t\t\t\tcontent: content,\n\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.diffCancel(item, diffItem);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tasync diffCancel(item, diffItem) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log(diffItem)\n\t\t\t\t\t// 如果有具体的差价申请项，使用差价申请的ID，否则使用订单ID\n\t\t\t\t\tconst cancelId = diffItem.id \n\t\t\t\t\tconst res = await this.$api.shifu.diffCancel({\n\t\t\t\t\t\tid:cancelId\n\t\t\t\t\t});\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '差价申请已取消',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.refreshList();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '取消失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error in diffCancel:', err);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tcancellModal(item) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '取消订单',\n\t\t\t\t\tcontent: '确定要取消此订单吗？',\n\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.$api.shifu.orderCancel({\n\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tif (res.code === \"200\") { // Assuming \"200\" is success code\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '订单已取消'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tthis.getList();\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '取消失败'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: '请求失败'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tconsole.error('Error cancelling order:', err);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tdingyue() {\n\t\t\t\tconsole.log('dingyue called');\n\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\tconst requiredTmplId = '9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY';\n\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// Ensure requiredTmplId is included, select 2 more randomly\n\t\t\t\tconst otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);\n\t\t\t\tconst shuffled = otherTmplIds.sort(() => 0.5 - Math.random());\n\t\t\t\tconst selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];\n\t\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\ttemplateId: id,\n\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t}));\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\t\tlet count = 0;\n\t\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// Do not push if templateCategoryId is 5\n\t\t\t\t\t\t\t\t// else {\n\t\t\t\t\t\t\t\t// \tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t// }\n\t\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tloadMore() {\n\t\t\t\tif (this.status === 'nomore' || this.isLoading) return;\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.status = 'loading';\n\n\t\t\t\tconst nextPage = this.page + 1;\n\t\t\t\tthis.fetchOrders(nextPage, false);\n\t\t\t},\n\n\t\t\trefreshList() {\n\t\t\t\tthis.page = 0;\n\t\t\t\tthis.orderList = [];\n\t\t\t\tthis.status = 'loadmore';\n\t\t\t\tthis.getList();\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t}, 1000);\n\t\t\t},\n\n\t\t\tfetchOrders(pageNum, replaceList = true) {\n\t\t\t\treturn this.$api.shifu.master_Order({\n\t\t\t\t\tcoachId: this.shifuId,\n\t\t\t\t\tpayType: this.currentIndex === 8 ? 7 : this.currentIndex,\n\t\t\t\t\tpageNum: pageNum,\n\t\t\t\t\tpageSize: this.limit\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif (res.code === '-1' || !res.data || !res.data.list) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg || '没有更多数据'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst list = Array.isArray(res.data.list) ? res.data.list : [];\n\t\t\t\t\t\tconst normalizedList = list\n\t\t\t\t\t\t\t.filter(item => this.currentIndex !== 8 || item.isAftermarket === 1)\n\t\t\t\t\t\t\t.map(item => ({\n\t\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\t\tpayType: parseInt(item.payType),\n\t\t\t\t\t\t\t\t// 确保 orderDiffPriceList 存在\n\t\t\t\t\t\t\t\torderDiffPriceList: Array.isArray(item.orderDiffPriceList) ? item.orderDiffPriceList : []\n\t\t\t\t\t\t\t}));\n\n\t\t\t\t\t\tif (replaceList) {\n\t\t\t\t\t\t\tthis.orderList = normalizedList;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.orderList = [...this.orderList, ...normalizedList];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis.page = pageNum;\n\t\t\t\t\t\tthis.status = list.length < this.limit ? 'nomore' : 'loadmore';\n\t\t\t\t\t}\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\treturn res;\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error loading data:', err);\n\t\t\t\t\treturn Promise.reject(err);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tgoDetail(item) {\n\t\t\t\tuni.setStorageSync('orderdetails', item);\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/shifu/master_order_my?id=${item.id}`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tgoUrl(e) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: e\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tshowConfirmModal(item, action) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: action === 'queren' ? '确认到达' : '开始服务',\n\t\t\t\t\tcontent: '请确认操作：' + (action === 'queren' ? '确认到达' : '开始服务'),\n\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tif (action === 'queren') {\n\t\t\t\t\t\t\t\tthis.queren(item);\n\t\t\t\t\t\t\t} else if (action === 'startFu') {\n\t\t\t\t\t\t\t\tthis.startFu(item);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tasync startFu(item) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.shifuqueren({\n\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\tpayType: 6\n\t\t\t\t\t});\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.refreshList();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error in startFu:', err);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tasync queren(item) {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.shifuqueren({\n\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\tpayType: 5\n\t\t\t\t\t});\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '操作成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.refreshList();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '操作失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error in queren:', err);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tupdateHigh(options) {\n\t\t\t\tconst shiInfo = uni.getStorageSync('shiInfo');\n\t\t\t\tif (!shiInfo) {\n\t\t\t\t\tconsole.log('No shiInfo, skipping updateHighlight');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet shiInfoid;\n\t\t\t\ttry {\n\t\t\t\t\tshiInfoid = JSON.parse(shiInfo);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('Error parsing shiInfo:', e);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.$api.service.updataHighlight({\n\t\t\t\t\tuserId: shiInfoid.id,\n\t\t\t\t\trole: 2,\n\t\t\t\t\tpayType: options.tab\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log(res);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('Error updating highlight:', err);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tgetList() {\n\t\t\t\tif (this.isLoading) return;\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.status = 'loading';\n\t\t\t\tthis.fetchOrders(1, true);\n\t\t\t},\n\n\t\t\thandleHeader(item) {\n\t\t\t\tif (this.currentIndex === item.value) return;\n\t\t\t\tthis.currentIndex = item.value;\n\t\t\t\tthis.page = 0;\n\t\t\t\tthis.orderList = [];\n\t\t\t\tthis.status = 'loadmore';\n\t\t\t\tthis.getList();\n\t\t\t\tthis.updateHigh({\n\t\t\t\t\ttab: item.value\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tlet shiInfo = uni.getStorageSync('shiInfo') || '{}';\n\t\t\ttry {\n\t\t\t\tthis.shifuId = JSON.parse(shiInfo).id;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('Error parsing shiInfo:', e);\n\t\t\t\tthis.shifuId = '';\n\t\t\t}\n\n\t\t\tif (options.tab) {\n\t\t\t\tthis.currentIndex = parseInt(options.tab);\n\t\t\t}\n\t\t\tthis.updateHigh(options);\n\t\t\tthis.getList();\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground-color: #F8F8F8;\n\t\theight: 100vh;\n\t\toverflow: auto;\n\t\tpadding-top: 100rpx;\n\n\t\t.header {\n\t\t\tposition: fixed;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tz-index: 100;\n\t\t\twidth: 750rpx;\n\t\t\theight: 100rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-around;\n\t\t\talign-items: center;\n\n\t\t\t.header_item {\n\t\t\t\tmax-width: 85rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\tflex-wrap: wrap;\n\n\t\t\t\t.blue {\n\t\t\t\t\tmargin-top: 8rpx;\n\t\t\t\t\twidth: 38rpx;\n\t\t\t\t\theight: 6rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.main {\n\t\t\tpadding: 20rpx 30rpx;\n\t\t\tmin-height: calc(100vh - 100rpx);\n\n\t\t\t.main_item {\n\t\t\t\twidth: 690rpx;\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tpadding: 28rpx 36rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tbox-sizing: border-box;\n\n\t\t\t\t.head {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\n\t\t\t\t\t.no {\n\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.mid {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.lef {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.righ {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.bot {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: flex-end;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tgap: 20rpx;\n\n\t\t\t\t\t.qzf {\n\t\t\t\t\t\twidth: 148rpx;\n\t\t\t\t\t\theight: 48rpx;\n\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tline-height: 48rpx;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 子订单样式\n\t\t\t\t.sub_orders {\n\t\t\t\t\tmargin-top: 30rpx;\n\t\t\t\t\tpadding-top: 20rpx;\n\t\t\t\t\tborder-top: 1px solid #f0f0f0;\n\n\t\t\t\t\t.sub_title {\n\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sub_item {\n\t\t\t\t\t\tbackground: #f8f9fa;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t\t\t.sub_head {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t\t\t\t.sub_no {\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t\tmax-width: 400rpx;\n\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.sub_status {\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.sub_content {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\talign-items: flex-end;\n\n\t\t\t\t\t\t\t.sub_info {\n\t\t\t\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t\t\t\t.sub_amount {\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.sub_reason {\n\t\t\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t\t\tmax-width: 300rpx;\n\t\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.sub_time {\n\t\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.sub_actions {\n\t\t\t\t\t\t\t\tflex-shrink: 0;\n\n\t\t\t\t\t\t\t\t.sub_qzf {\n\t\t\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\t\t\t\tbackground: #ff6b6b;\n\t\t\t\t\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\t\t\t\t\tfont-size: 18rpx;\n\t\t\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.slot-content {\n\t\tpadding: 30rpx;\n\t}\n\n\t.upload-container {\n\t\tmargin-top: 16rpx;\n\t\tpadding: 20rpx;\n\t\tborder: 1rpx dashed #ccc;\n\t\tborder-radius: 8rpx;\n\t\tbackground: #fafafa;\n\t}\n\n\t.parts-img {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tmargin-right: 12rpx;\n\t\tmargin-bottom: 12rpx;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t/* 简洁的差价申请弹窗样式 */\n\t.diff-apply-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 9999;\n\t}\n\n\t.modal-content {\n\t\twidth: 90%;\n\t\tmax-width: 600rpx;\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t\toverflow: hidden;\n\t}\n\n\t.modal-header {\n\t\tposition: relative;\n\t\tpadding: 40rpx 50rpx 20rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t}\n\n\t.close-btn {\n\t\tposition: absolute;\n\t\ttop: 20rpx;\n\t\tright: 20rpx;\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 50%;\n\t\tbackground: #f5f5f5;\n\t\ttransition: background 0.2s;\n\t}\n\n\t.close-btn:active {\n\t\tbackground: #e0e0e0;\n\t}\n\n\t.close-icon {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1;\n\t}\n\n\t.modal-body {\n\t\tpadding: 40rpx;\n\t}\n\n\t.modal-footer {\n\t\tpadding: 20rpx 40rpx 40rpx;\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.btn-cancel, .btn-confirm {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\ttransition: all 0.2s;\n\t}\n\n\t.btn-cancel {\n\t\tbackground: #f8f8f8;\n\t\tcolor: #666;\n\t\tborder: 1rpx solid #ddd;\n\t}\n\n\t.btn-cancel:active {\n\t\tbackground: #e8e8e8;\n\t}\n\n\t.btn-confirm {\n\t\tbackground: #007aff;\n\t\tcolor: #fff;\n\t\tborder: 1rpx solid #007aff;\n\t}\n\n\t.btn-confirm:active {\n\t\tbackground: #0056cc;\n\t}\n\n\t/* 简洁表单样式 */\n\t.modal-body /deep/ .u-form-item {\n\t\tmargin-bottom: 24rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.modal-body /deep/ .u-form-item:last-child {\n\t\tborder-bottom: none;\n\t\tmargin-bottom: 0;\n\t}\n\n\t.modal-body /deep/ .u-form-item__label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 12rpx;\n\t\tfont-weight: 500;\n\t}\n\n\t.modal-body /deep/ .u--input__content {\n\t\tbackground: #f8f8f8;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t.modal-body /deep/ .u--textarea__content {\n\t\tbackground: #f8f8f8;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmin-height: 120rpx;\n\t}\n\n\t/* 原因类型显示样式 */\n\t.reason-type-display {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 16rpx;\n\t\tbackground: #f8f8f8;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t.reason-type-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t}\n\n\t.reason-type-badge {\n\t\tbackground: #007aff;\n\t\tcolor: #fff;\n\t\tfont-size: 20rpx;\n\t\tpadding: 4rpx 12rpx;\n\t\tborder-radius: 12rpx;\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_my_order.vue?vue&type=style&index=0&id=b530dfe4&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754791170594\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}