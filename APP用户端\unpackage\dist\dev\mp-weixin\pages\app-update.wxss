@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.app-update-page.data-v-05362ea4 {
  min-height: 100vh;
  background: #f5f5f5;
}
.navbar.data-v-05362ea4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}
.navbar .nav-left.data-v-05362ea4, .navbar .nav-right.data-v-05362ea4 {
  width: 80rpx;
}
.navbar .nav-icon.data-v-05362ea4 {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}
.navbar .nav-title.data-v-05362ea4 {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.version-info.data-v-05362ea4 {
  display: flex;
  align-items: center;
  padding: 40rpx 24rpx;
  background: #fff;
  margin-bottom: 20rpx;
}
.version-info .app-icon.data-v-05362ea4 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-right: 24rpx;
}
.version-info .app-icon image.data-v-05362ea4 {
  width: 100%;
  height: 100%;
}
.version-info .version-details.data-v-05362ea4 {
  flex: 1;
}
.version-info .version-details .app-name.data-v-05362ea4 {
  display: block;
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}
.version-info .version-details .version-text.data-v-05362ea4 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.check-update-section.data-v-05362ea4 {
  padding: 0 24rpx 20rpx;
}
.check-update-section .check-btn.data-v-05362ea4 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.check-update-section .check-btn.checking.data-v-05362ea4 {
  background: #ccc;
}
.check-update-section .check-btn.data-v-05362ea4:active:not(:disabled) {
  opacity: 0.8;
}
.check-update-section .test-btn.data-v-05362ea4 {
  width: 100%;
  height: 88rpx;
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}
.check-update-section .test-btn.data-v-05362ea4:active {
  opacity: 0.8;
}
.update-info.data-v-05362ea4 {
  background: #fff;
  margin: 0 24rpx 20rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}
.update-info .info-header.data-v-05362ea4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.update-info .info-header .info-title.data-v-05362ea4 {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}
.update-info .info-header .update-badge.data-v-05362ea4 {
  background: #ff4757;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}
.update-info .info-content.data-v-05362ea4 {
  margin-bottom: 32rpx;
}
.update-info .info-content .info-desc.data-v-05362ea4 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.update-info .info-actions.data-v-05362ea4 {
  display: flex;
  gap: 20rpx;
}
.update-info .info-actions .update-btn.data-v-05362ea4 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
}
.update-info .info-actions .update-btn.primary.data-v-05362ea4 {
  background: #667eea;
  color: #fff;
}
.update-info .info-actions .update-btn.secondary.data-v-05362ea4 {
  background: #f8f8f8;
  color: #666;
}
.update-info .info-actions .update-btn.data-v-05362ea4:active:not(:disabled) {
  opacity: 0.8;
}
.no-update.data-v-05362ea4 {
  text-align: center;
  padding: 60rpx 24rpx;
}
.no-update .no-update-text.data-v-05362ea4 {
  font-size: 32rpx;
  color: #52c41a;
}
.update-notes.data-v-05362ea4 {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}
.update-notes .notes-title.data-v-05362ea4 {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.update-notes .notes-content .notes-item.data-v-05362ea4 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 12rpx;
}

