{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Settle.vue?3ac1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Settle.vue?a787", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Settle.vue?3761", "uni-app:///shifu/Settle.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Settle.vue?465c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Settle.vue?69eb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "configInfo", "data", "flag", "showSh", "shInfo", "status", "xuzhi", "title", "show", "getentryNotices", "showSubmitConfirm", "arr", "text", "color", "serviceInfoName", "configInfos", "entryNotice", "form", "<PERSON><PERSON><PERSON>", "sex", "mobile", "workTime", "city", "cityId", "address", "idCode", "userInfophone", "id_card1", "id_card2", "selfImg", "showCity", "loading", "columnsCity", "computed", "isFormDisabled", "methods", "goMap", "uni", "scope", "success", "console", "that", "fail", "navigateToSkills", "url", "confirmCity", "map", "join", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "index", "picker", "shDetail", "imgUpload", "imgtype", "showConfirmPopup", "confirmSubmit", "cancelSubmit", "submit", "icon", "duration", "id", "userId", "serviceIds", "idCard", "lat", "lng", "labelId", "setTimeout", "cancelModel", "confirmModel", "getcity", "seeInfo", "res", "path", "onLoad", "onShow", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC6Gz2B;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EAAA,GACA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,MACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA,IACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAd;QACAe;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MAEAC;QACAC;QACAC;UACAF;YACAE;cACAC;cACAC;cACAA;cACAA;cACAA;YACA;UACA;QACA;QACAC;UACAF;QACA;MACA;IAaA;IACAG;MACAN;QACAO;MACA;IACA;IACAC;MAAA;MACA,6BACAC;QACA;UACA;QACA;UACA;QACA;MACA,GACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA,IACAC,cAGAC,EAHAD;QACAE,QAEAD,EAFAC;QAAA,YAEAD,EADAE;QAAAA;MAEA;QACA;UACAA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAd;MACA;QAAAe;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAnB;MACA;QAAA;QACA;UACAH;YACAuB;YACArD;UACA;UACA;QACA;MACA;MAEA;MACA;QACA8B;UACAuB;UACArD;UACAsD;QACA;QACA;MACA;MAEA;QACAC;QACA5C;QACAE;QACAI;QACAF;QACAC;QACAwC;QACAtC;QACAuC;QACAC,SACA,4EACA,6BACA,IACA,4EACA,6BACA,GACA;QACApC,4CACA;UAAA;QAAA,KACA;QACAV;QACAP;QACAS;QACA6C;QACAC;QACAC;MACA;MACA5B;MACA;QACA;UACAH;YACAuB;YACArD;UACA;UACA8D;YACAhC;cACAO;YACA;UACA;UACAP;UACAA;QACA;UACAA;YACAuB;YACArD;UACA;UACA8D;YACAhC;cACAO;YACA;UACA;UACAP;UACAA;QACA;MACA;QACAA;UACAuB;UACArD;QACA;QACA8B;QACAA;QACAgC;UACAhC;YACAO;UACA;QACA;MACA;IACA;IACA0B;MACAjC;IACA;IACAkC;MACA;IACA;IACAC;MAAA;MACA;QACAhC;QACA;QACA;UACA;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAiC;MAAA;MACA;QACA;UACA;UACAjC;UACA;UACA,4DACA,cACAkC;YACA/C;cAAAgD;YAAA;YACA/C;cAAA+C;YAAA;YACA9C;cAAA;gBAAA8C;cAAA;YAAA;UAAA,EACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACApC;MACA;MACAA;IACA;IACA;IACA;IACAA;IACA;IACA;IACA;MACA;IACA;IACAA;IACA;IACA;EACA;EACAqC;IAAA;IACA;IACAxC;MACA;QACA;MACA;IACA;EACA;EACAyC;IACAzC;IACAA;IACAA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACvaA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/Settle.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/Settle.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Settle.vue?vue&type=template&id=458b6785&scoped=true&\"\nvar renderjs\nimport script from \"./Settle.vue?vue&type=script&lang=js&\"\nexport * from \"./Settle.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Settle.vue?vue&type=style&index=0&id=458b6785&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"458b6785\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/Settle.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=template&id=458b6785&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showSh = false\n    }\n    _vm.e2 = function ($event) {\n      !_vm.isFormDisabled && (_vm.showCity = true)\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <u-picker\n      v-if=\"flag\"\n      :show=\"showCity\"\n      ref=\"uPicker\"\n      :loading=\"loading\"\n      :columns=\"columnsCity\"\n      @change=\"changeHandler\"\n      keyName=\"title\"\n      @cancel=\"showCity = false\"\n      @confirm=\"confirmCity\"\n    ></u-picker>\n    <u-modal\n      :show=\"show\"\n      :title=\"title\"\n      :showCancelButton=\"true\"\n      confirmText=\"同意\"\n      cancelText=\"不同意\"\n      @confirm=\"confirmModel\"\n      @cancel=\"cancelModel\"\n    >\n      <view class=\"slot-content\">\n        <rich-text :nodes=\"configInfos.entryNotice?configInfos.entryNotice:getentryNotices\"></rich-text>\n      </view>\n    </u-modal>\n    <u-modal\n      v-if=\"shInfo.status == 4\"\n      :show=\"showSh\"\n      title=\"驳回原因\"\n      confirmText=\"确定\"\n      @confirm=\"showSh = false\"\n      :content=\"shInfo.shText\"\n    ></u-modal>\n    <u-modal\n      :show=\"showSubmitConfirm\"\n      title=\"提交确认\"\n      :showCancelButton=\"true\"\n      confirmText=\"确认\"\n      cancelText=\"取消\"\n      @confirm=\"confirmSubmit\"\n      @cancel=\"cancelSubmit\"\n    >\n      <view class=\"slot-content\">\n        <text>确定要提交信息吗？</text>\n      </view>\n    </u-modal>\n    <view\n      class=\"header\"\n      v-if=\"shInfo.status !== -1\"\n      :style=\"'color:' + arr[shInfo.status - 1].color\"\n      @click=\"shDetail\"\n    >\n      {{ arr[shInfo.status - 1].text }}\n    </view>\n\n    <view class=\"main\">\n      <view class=\"main_item\">\n        <view class=\"title\"><span>*</span>姓名</view>\n        <input type=\"text\" v-model=\"form.coachName\" placeholder=\"请输入姓名\" :disabled=\"isFormDisabled\" />\n      </view>\n     <view class=\"main_item\">\n       <view class=\"title\"><span>*</span>手机号</view>\n       <input type=\"text\" v-model=\"form.mobile\" placeholder=\"请输入手机号\" :disabled=\"isFormDisabled\" />\n     </view>\n\n     <!-- <view class=\"main_item\">\n        <view class=\"title\"><span>*</span>选择服务</view>\n        <input type=\"text\" v-model=\"serviceInfoName?serviceInfoName:form.serviceInfo\" placeholder=\"请选择服务\" disabled :class=\"{ 'input-disabled': !isFormDisabled }\" @click=\"!isFormDisabled && navigateToSkills()\">\n      </view> -->\n<!--     <view class=\"main_item\">\n        <view class=\"title\"><span>*</span>选择区域</view>\n        <input\n          type=\"text\"\n          v-model=\"form.city\"\n          placeholder=\"请选择代理区域\"\n          disabled\n          :class=\"{ 'input-disabled': !isFormDisabled }\"\n          @click=\"!isFormDisabled && (showCity = true)\"\r\n\t\t  \n        />\n      </view> -->\n  <view class=\"main_item\">\n        <view class=\"title\"><span>*</span>选择区域</view>\n        <input\n          type=\"text\"\n          v-model=\"form.city\"\n          placeholder=\"请选择代理区域\"\n          disabled\n          :class=\"{ 'input-disabled': !isFormDisabled }\"\n          @click=\"!isFormDisabled && (showCity = true)\"\r\n\t\t  \n        />\n      </view>\n      <view @tap=\"goMap\" class=\"main_item\">\n        <view class=\"title\"><span>*</span>详细地址</view>\n        <view class=\"address\">\n\t\t\t <input v-model=\"form.address\" placeholder=\"请点击选取地址\" :disabled=\"isFormDisabled\" />\n        </view>\n      </view>\n    </view>\n    <view class=\"footer\" v-if=\"shInfo.status === -1 || shInfo.status === 4\">\n      <view class=\"btn\" @click=\"showConfirmPopup\">立即提交</view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport Upload from '@/components/upload.vue';\nimport { mapState } from 'vuex';\nexport default {\n  components: {\n    Upload,\n    ...mapState({\n      configInfo: (state) => state.config.configInfo,\n    })\n  },\n  data() {\n    return {\n      flag: false,\n      showSh: false,\n      shInfo: { status: -1 },\n      xuzhi: '',\n      title: '入驻须知',\n      show: false,\n      getentryNotices: '',\n      showSubmitConfirm: false,\n      arr: [\n        { text: '信息审核中，请稍作等待', color: '#FE921B' },\n        { text: '审核成功', color: '#07C160' },\n        {},\n        { text: '审核失败>点击查看', color: '#E72427' },\n      ],\n      serviceInfoName: '',\n      configInfos: {\n        entryNotice: ''\n      },\n      form: {\n        coachName: '',\n        sex: 0,\n        mobile: this.userInfophone,\n        workTime: '',\n        city: '',\n        cityId: [],\n        address: '',\n        idCode: '',\n        userInfophone: '',\n        text: '',\n        id_card1: [],\n        id_card2: [],\n        selfImg: [],\n      },\n      showCity: false,\n      loading: false,\n      columnsCity: [[], [], []],\n    };\n  },\n  computed: {\n    isFormDisabled() {\n      return !(this.shInfo.status === -1 || this.shInfo.status === 4);\n    }\n  },\n  methods: {\n    goMap() {\n      let that = this\n      // #ifdef MP-WEIXIN\n      uni.authorize({\n        scope: 'scope.userLocation',\n        success(res) {\n          uni.chooseLocation({\n            success: function(res) {\n              console.log(res)\n              that.form.address = res.name\n              that.form.addressInfo = res.address\n              that.form.lng = res.longitude\n              that.form.lat = res.latitude\n            }\n          });\n        },\n        fail(err) {\n          console.error(err)\n        }\n      })\n      // #endif\n      // #ifdef APP\n      uni.chooseLocation({\n        success: function(res) {\n          console.log(res)\n          that.form.address = res.name\n          that.form.addressInfo = res.address\n          that.form.lng = res.longitude\n          that.form.lat = res.latitude\n        }\n      });\n      // #endif\n    },\n    navigateToSkills() {\n      uni.navigateTo({\n        url: '/shifu/skillsIndex'\n      });\n    },\n    confirmCity(Array) {\n      this.form.city = Array.value\n        .map((item, index) => {\n          if (item == undefined) {\n            return this.columnsCity[index][0].title;\n          } else {\n            return item.title;\n          }\n        })\n        .join('-');\n      this.form.cityId = Array.value.map((e, j) => {\n        if (e == undefined) {\n          return this.columnsCity[j][0].id;\n        } else {\n          return e.id;\n        }\n      });\n      this.showCity = false;\n    },\n    changeHandler(e) {\n      const {\n        columnIndex,\n        index,\n        picker = this.$refs.uPicker,\n      } = e;\n      if (columnIndex === 0) {\n        this.$api.shifu.getCity(this.columnsCity[0][index].id).then((res) => {\n          picker.setColumnValues(1, res);\n          this.columnsCity[1] = res;\n          this.$api.service.getCity(res[0].id).then((res1) => {\n            picker.setColumnValues(2, res1);\n            this.columnsCity[2] = res1;\n          });\n        });\n      } else if (columnIndex === 1) {\n        this.$api.shifu.getCity(this.columnsCity[1][index].id).then((res) => {\n          picker.setColumnValues(2, res);\n          this.columnsCity[2] = res;\n        });\n      }\n    },\n    shDetail() {\n      if (this.shInfo.status != 4) return;\n      this.showSh = true;\n    },\n    imgUpload(e) {\n      console.log('imgUpload event:', e);\n      const { imagelist, imgtype } = e;\n      this.$set(this.form, imgtype, imagelist);\n    },\n    showConfirmPopup() {\n      this.showSubmitConfirm = true;\n    },\n    confirmSubmit() {\n      this.showSubmitConfirm = false;\n      this.submit();\n    },\n    cancelSubmit() {\n      this.showSubmitConfirm = false;\n    },\n    submit() {\n      const requiredFields = ['coachName', 'mobile', 'city', 'cityId', 'address'];\n      console.log(requiredFields)\n      for (let key of requiredFields) {\n        if (this.form[key] === '' || (Array.isArray(this.form[key]) && this.form[key].length === 0)) {\n          uni.showToast({\n            icon: 'none',\n            title: '请填写必填项',\n          });\n          return;\n        }\n      }\n\n      let phoneReg = /^1[3456789]\\d{9}$/;\n      if (!phoneReg.test(this.form.mobile)) {\n        uni.showToast({\n          icon: 'none',\n          title: '请填写正确的手机号',\n          duration: 1000,\n        });\n        return;\n      }\n\n      let obj = {\n        id: this.form.id || 0,\n        coachName: this.form.coachName,\n        mobile: this.form.mobile,\n        address: this.form.address,\n        city: this.form.city,\n        cityId: Array.isArray(this.form.cityId) ? this.form.cityId.join(',') : this.form.cityId,\n        userId: uni.getStorageSync('userId') || '',\n        idCode: this.form.idCode,\n        serviceIds: uni.getStorageSync('selectedServices') || this.form.serviceIds || '',\n        idCard: [\n          this.form.id_card1 && this.form.id_card1[0] && this.form.id_card1[0].path\n            ? this.form.id_card1[0].path\n            : '',\n          this.form.id_card2 && this.form.id_card2[0] && this.form.id_card2[0].path\n            ? this.form.id_card2[0].path\n            : ''\n        ],\n        selfImg: Array.isArray(this.form.selfImg)\n          ? this.form.selfImg.map(img => img.path || '')\n          : [],\n        sex: this.form.sex,\n        text: this.form.text,\n        workTime: parseInt(this.form.workTime),\n        lat: this.form.lat ? this.form.lat : String(uni.getStorageSync('lat')) || '33.06457',\n        lng: this.form.lng? this.form.lng : String(uni.getStorageSync('lng')) || '115.25811',\n        labelId: 0\n      };\n\tconsole.log(obj)\n      this.$api.shifu.masterEnter(obj).then(res => {\n        if (res.data === \"ok\") {\n          uni.showToast({\n            icon: 'success',\n            title: '提交成功，等待审核',\n          });\n          setTimeout(() => {\n            uni.redirectTo({\n              url: '/shifu/mine'\n            });\n          }, 2000);\n          uni.removeStorageSync('selectedServiceNames');\n          uni.removeStorageSync('selectedServices');\n        } else {\n          uni.showToast({\n            icon: 'none',\n            title: res.msg || '提交失败',\n          });\n          setTimeout(() => {\n            uni.redirectTo({\n              url: '/shifu/mine'\n            });\n          }, 2000);\n          uni.removeStorageSync('selectedServiceNames');\n          uni.removeStorageSync('selectedServices');\n        }\n      }).catch(err => {\n        uni.showToast({\n          icon: 'none',\n          title: err.msg || '网络错误'\n        });\n        uni.removeStorageSync('selectedServiceNames');\n        uni.removeStorageSync('selectedServices');\n        setTimeout(() => {\n          uni.redirectTo({\n            url: '/shifu/mine'\n          });\n        }, 2000);\n      });\n    },\n    cancelModel() {\n      uni.navigateBack();\n    },\n    confirmModel() {\n      this.show = false;\n    },\n    getcity(e) {\n      this.$api.shifu.getCity(e).then((res) => {\r\n\t\t  console.log(res)\n        this.columnsCity[0] = res;\n        this.$api.service.getCity(res[0].id).then((res1) => {\n          this.columnsCity[1] = res1;\n          this.$api.service.getCity(res1[0].id).then((res2) => {\n            this.columnsCity[2] = res2;\n            this.flag = true;\n          });\n        });\n      });\n    },\n    seeInfo() {\n      this.$api.shifu.getMaster().then((ress) => {\n        if (ress && Object.keys(ress).length !== 0) {\r\n\t\t\tlet res=ress.data\n          console.log(res);\n          this.shInfo = res;\n          this.form = {\n            ...this.form,\n            ...res,\n            id_card1: res.idCard && res.idCard[0] ? [{ path: res.idCard[0] }] : [],\n            id_card2: res.idCard && res.idCard[1] ? [{ path: res.idCard[1] }] : [],\n            selfImg: res.selfImg ? res.selfImg.map((item) => ({ path: item })) : [],\n          };\n        } else {\n          this.show = true;\n        }\n      });\n    },\n  },\n  onLoad() {\n    this.$api.base.getConfig().then(res => {\n      console.log(res);\n      this.getentryNotices = res.entryNotice;\n      console.log(this.getentryNotices);\n    });\n    this.serviceInfoName = uni.getStorageSync(\"selectedServiceNames\");\n    let userphone = uni.getStorageSync(\"userInfo\");\n    console.log(userphone)\n    this.form.mobile = userphone.phone\n    const configInfo = uni.getStorageSync('configInfo');\n    if (configInfo) {\n      this.configInfos = configInfo;\n    }\n    console.log(this.configInfos);\n    this.getcity(0);\n    this.seeInfo();\n  },\n  onShow() {\n    this.serviceInfoName = uni.getStorageSync(\"selectedServiceNames\") || '';\n    uni.$on('getShInfo', (data) => {\n      if (data) {\n        this.seeInfo();\n      }\n    });\n  },\r\n  onUnload() {\r\n    uni.$off('getShInfo');\r\n    uni.removeStorageSync('selectedServiceNames');\r\n    uni.removeStorageSync('selectedServices');\r\n  },\n\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  padding-bottom: 200rpx;\n\n  .header {\n    width: 750rpx;\n    height: 58rpx;\n    background: #fff7f1;\n    line-height: 58rpx;\n    text-align: center;\n    font-size: 28rpx;\n    font-weight: 400;\n  }\n\n  .main {\n    padding: 40rpx 30rpx;\n\n    .main_item {\n      margin-bottom: 20rpx;\n\n      .title {\n        margin-bottom: 20rpx;\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #333333;\n\n        span {\n          color: #e72427;\n        }\n      }\n\n      input {\n        width: 690rpx;\n        height: 110rpx;\n        background: #f8f8f8;\n        font-size: 28rpx;\n        font-weight: 400;\n        line-height: 110rpx;\n        padding: 0 40rpx;\n        box-sizing: border-box;\n\n        &:disabled {\n          background: #f0f0f0;\n          color: #999;\n          cursor: not-allowed;\n        }\n      }\n\n      .input-disabled {\n        background: #f8f8f8 !important;\n        color: #333 !important;\n        cursor: default !important;\n      }\n\n      .card {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .card_item {\n          width: 332rpx;\n          height: 332rpx;\n          background: #f2fafe;\n          border-radius: 16rpx;\n          overflow: hidden;\n\n          .top {\n            height: 266rpx;\n            width: 100%;\n            padding-top: 40rpx;\n\n            .das {\n              margin: 0 auto;\n              width: 266rpx;\n              height: 180rpx;\n              border: 2rpx dashed #2e80fe;\n              padding-top: 28rpx;\n\n              .up {\n                margin: 0 auto;\n                width: 210rpx;\n                height: 130rpx;\n              }\n            }\n          }\n\n          .bottom {\n            height: 66rpx;\n            width: 100%;\n            background-color: #2e80fe;\n            font-size: 28rpx;\n            font-weight: 400;\n            color: #ffffff;\n            text-align: center;\n            line-height: 66rpx;\n          }\n        }\n      }\n\n      .disabled-upload {\n        width: 100%;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5f5f5;\n        border-radius: 8rpx;\n\n        .image-preview {\n          width: 100%;\n          height: 100%;\n\n          image {\n            width: 100%;\n            height: 100%;\n            border-radius: 8rpx;\n          }\n        }\n\n        .upload-placeholder {\n          color: #999;\n          font-size: 24rpx;\n          text-align: center;\n        }\n      }\n\n      .disabled-upload-list {\n        display: flex;\n        justify-content: center;\n        width: 100%;\n\n        .image-list {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 20rpx;\n          justify-content: center;\n\n          .image-item {\n            width: 200rpx;\n            height: 200rpx;\n            border-radius: 8rpx;\n            overflow: hidden;\n\n            image {\n              width: 100%;\n              height: 100%;\n            }\n          }\n        }\n\n        .upload-placeholder {\n          width: 200rpx;\n          height: 200rpx;\n          background: #f5f5f5;\n          border-radius: 8rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #999;\n          font-size: 24rpx;\n        }\n      }\n    }\n  }\n\n  .footer {\n    padding: 52rpx 30rpx;\n    width: 750rpx;\n    background: #ffffff;\n    border-top: 1rpx solid #e8e8e8;\n    position: fixed;\n    bottom: 0;\n    z-index: 999;\n\n    .btn {\n      width: 690rpx;\n      height: 98rpx;\n      background: #2e80fe;\n      border-radius: 50rpx;\n      font-size: 32rpx;\n      font-weight: 500;\n      color: #ffffff;\n      line-height: 98rpx;\n      text-align: center;\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=style&index=0&id=458b6785&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Settle.vue?vue&type=style&index=0&id=458b6785&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754806313425\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}