import {
	req
} from '../../utils/req.js';
export default {
	// 获取轮播图 1，2
	index(param) {
		return req.get(`coach/banner/list`)
	},
	// 获取 一口价 高价值 
	// getnewshiFuOrderList(param){
	// 	return req.get(`shiFu/order/shiFuOrderList`)
	// },
	indexQuote(param) {
		return req.post("coach/order/hall", param)
		// const {
		// 	lng,
		// 	lat,
		// 	pageNum =1, pageSize =10,
		// 	parentId=0,
		// 	quotationNum=true
		// } = param;
		

		// return req.post(`shiFu/order/shiFuOrderList?lng=${lng}&lat=${lat}&quotationNum=${quotationNum}&parentId=${parentId}`)
	},
	gettext(param)
	{
		return req.get("coach/skill/getHard", param)
	},
	// 保证金退款
	tuikuanBzj(param){
		return req.post("coach/service/refundMargin", param)
	},
	// 一口价接单
	oneAcceptOrders(param)
	{
		return req.post("coach/order/acceptOrders", param)
	},

	//师傅入驻  1,2
	masterEnter(param) {
		return req.post("coach/apply", param)
	},
	// 师傅鉴权 1，2
	getshifstutas(param) {
		const {
			userId,
			
		} = param;
		return req.get(`coach/auth/status?userId=${userId}`)
	},
	master_Order(param) {
		const {
			coachId,
			payType = 0,pageNum =1, pageSize =10
		} = param;
		return req.get(`coach/orderDetails/allOrderList?coachId=${coachId}&payType=${payType}&pageNum=${pageNum}&pageSize=${pageSize}`)

	},
	coachCash(param){
		return req.get("coach/coachInfo", param)
	},
	// nowPay(param){
	// return req.post("shiFu/service/payMargin", param)
	// },

	shifuqueren(param){
		return req.post("coach/orderDetails/updateOrder", param)
		},
	updateMessagePush(param)
	{
		return req.post("shiFu/service/updateMessagePush", param)
	},
	
	//师傅信息查看
	masterSee(param) {
		return req.get("/massage/app/IndexUser/coachInfo", param)
	},
	//省市区
	// getCity(param) {
	// 	return req.get(`city/citySelect?pid=${param}`)
	// },
	getCity(param) {
		return req.get(`core/city/tree?pid=${param}`)
	},
	getSInfo(param) {
		return req.get("coach/coachInfo", param)
	},
	getNewSkillInfo(param) {
		return req.get("coach/skill/tags", param)
	},
	// indexQuote(param) {
	// 	return req.get("shiFu/order/shiFuOrderList", param)
	// },

	// /师傅押金缴纳
	masterPayY(param) {
		return req.get("/massage/app/IndexUser/cashPledge", param)
	},
	// 师傅入驻
	
	updataInfoSF(param) {
		return req.post("coach/updateCoachInfo", param)
	},
	// 更新师傅信息
	updateBao(param) {
		return req.post("coach/order/quote", param)
	},
	// 更新技能
	updataSkill(param) {
		return req.post("coach/skill/tags/update", param)
	},
	//获取技能
	getSkill(param) {
		return req.get("shiFu/service/serviceCate", param)
	},
	
	//获取技能
	getNewSkill(param) {
		return req.get("coach/skill/tags", param)
	},
	//师傅等级查询
	getGrade(param) {
		return req.get("coach/grade/info", param)
	},
	
	//查看当前师傅报价次数
	getQuotationCounts(param) {
		return req.get("coach/order/quotationCounts", param)
	},
	
	//师傅等级升级列表
	
	getGradeList(param) {
		return req.get("coach/grade/list", param)
	},
	//保证金
	seeBzj(param)
	{
		return req.get("coach/service/margin", param)
	},
	//收入明细
	incomeSee(param) {
		const {
			type,
			pageNum =1, pageSize =10
		} = param;
		return req.get(`coach/service/servicePrice?type=${type}&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	
	//保证金
	nowPay(param){
		const {
			payPrice,
			couponId,
			type
		} = param;
		
		return req.post(`coach/service/payMargin?payPrice=${payPrice}&couponId=${couponId}&type=${type}`)
	// return req.post(`shiFu/service/payMargin?payPrice=${param}`)
	},
	
	orderCancel(param){
		return req.post("coach/order/cancel", param)
	},
	
	upgradeGrade(param){
		return req.post("coach/grade/payUpgrade", param)
	},
	
	//师傅端发起订单补差价申请
	diffApply(param){
		return req.post("coach/diff/apply", param)
	},
	
	
	//师傅取消差价申请
	diffCancel(param){
		const {
			id
		} = param;
		
		return req.post(`coach/diff/cancel/${id}`)
	},
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	//
	//获取证书列表
	getCertList(param) {
		return req.get("coach/skill/certs", param)
	},
	//上传证书
	postCertList(param) {
		return req.post("coach/skill/certs/upload", param)
	},
	//删除证书
	delCertList(param) {
		return req.post("coach/skill/certs/delete", param)
	},
	// serviceCate(param) {
	// 	return req.get(`/massage/app/Index/serviceCate&city_id=${param}`)
	// },
	serviceCate(param) {
		return req.get("shiFu/service/serviceClassification", param)
	},

	masterBaolist(param) {
		const {
			coachId,
			pageNum =1, pageSize =10
		} = param;
		return req.get(`coach/order/quoted?coachId=${coachId}&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	updateQuxiaoBao(param) {
		return req.post("coach/order/cancelQuote", param)
	},
	//订单详情
	orderdetM(param) {
		return req.get(`coach/order/${param}`)
	},
	//获取师傅信息
	getMaster(param) {
		return req.get("coach/coachInfo", param)
	},

	// loginuserInfo(param) {
	// 	return req.post("api/v1/wxLogin", param)
	// },
	// userInfo(param) {
	// 	return req.get("user/info", param)
	// },
	//检测师傅信息
	checkMaster(param) {
		return req.post("shiFu/index/shiFuAuth", param)
	},
}