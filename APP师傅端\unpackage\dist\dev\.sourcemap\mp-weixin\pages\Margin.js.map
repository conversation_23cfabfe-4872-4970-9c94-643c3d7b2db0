{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/Margin.vue?7405", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/Margin.vue?02fe", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/Margin.vue?fb3f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/Margin.vue?9582", "uni-app:///pages/Margin.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/Margin.vue?1cd2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/Margin.vue?9518"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "methods", "getCurrentPlatform", "handleAppWechatPay", "console", "uni", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "title", "icon", "setTimeout", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "getMoney", "submit", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCWz2B;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MAAA;QAAA;MACAC;MACAC;QACA;QACAC;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEACA;QACAT;QACAC;UACAS;UACAC;QACA;QACAC;UACA;QACA;MACA,+DACA;QACAZ;QACA;UACAC;YACAS;YACAC;UACA;QACA;UACAV;YACAS;YACAC;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAE;MAAA;MACA;QACAC;QAAA;QACAC;QACAV;QACAW;QACAC;MACA;MACAjB;MACAC;QACA;QACAa;QACAC;QACAV;QACAC;QACAU;QACAC;QACAC;QACAC;UACA;UACAnB;UACAC;YACAS;YACAC;UACA;UACAC;YACA;UACA;QACA;QACAQ;UACA;UACApB;UACAA;UACA;YACAC;cACAS;cACAC;YACA;UACA;YACAV;cACAS;cACAC;YACA;UACA;UACAX;UACAC;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEAU;MAAA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACAtB;QAEA;UACA;UACAA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;QACA;MACA;QACAC;UACAU;UACAD;QACA;MACA;IACA;EACA;EACAa;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/Margin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/Margin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Margin.vue?vue&type=template&id=4952e6f2&scoped=true&\"\nvar renderjs\nimport script from \"./Margin.vue?vue&type=script&lang=js&\"\nexport * from \"./Margin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Margin.vue?vue&type=style&index=0&id=4952e6f2&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4952e6f2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/Margin.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=template&id=4952e6f2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"box\">\r\n\t\t\t<view class=\"money\">{{money}}</view>\r\n\t\t\t<view class=\"title\">保证金金额（元）</view>\r\n\t\t\t<view class=\"btn\" @click=\"submit\">缴纳保证金</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmoney:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 检查当前平台\r\n\t\t\tgetCurrentPlatform() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\treturn 'app-plus';\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\treturn 'mp-weixin';\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\treturn 'h5';\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn 'unknown';\r\n\t\t\t},\r\n\r\n\t\t\t// APP微信支付处理\r\n\t\t\thandleAppWechatPay(obj) {\r\n\t\t\t\tconsole.log(111)\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\"provider\": \"wxpay\",\r\n\t\t\t\t\t    orderInfo: 'orderInfo',\r\n\t\t\t\t\torderInfo: {\r\n\t\t\t\t\t\tappid: obj.appId,\r\n\t\t\t\t\t\tnoncestr: obj.nonceStr,\r\n\t\t\t\t\t\tpackage: 'Sign=WXPay',\r\n\t\t\t\t\t\tpartnerid: obj.partnerId,\r\n\t\t\t\t\t\tprepayid: obj.prepayId,\r\n\t\t\t\t\t\ttimestamp: String(obj.timestamp),\r\n\t\t\t\t\t\tsign: obj.sign\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('APP微信支付成功', res);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.getMoney()\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('APP微信支付失败:', err);\r\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '您已取消支付',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 微信小程序支付处理（保持原有逻辑）\r\n\t\t\thandleMiniProgramPay(obj) {\r\n\t\t\t\tconst paymentParams = {\r\n\t\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\r\n\t\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\t\tsignType: 'MD5',\r\n\t\t\t\t\tpaySign: obj.sign\r\n\t\t\t\t};\r\n\t\t\t\tconsole.log(JSON.stringify(paymentParams));\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\"provider\": 'wxpay',\r\n\t\t\t\t\ttimeStamp: String(obj.timestamp),\r\n\t\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\t\tpartnerid: obj.partnerId,\r\n\t\t\t\t\tsignType: \"MD5\",\r\n\t\t\t\t\tpaySign: obj.sign,\r\n\t\t\t\t\tappId: obj.appId,\r\n\t\t\t\t\tsuccess: (res1) => {\r\n\t\t\t\t\t\t// 支付成功回调\r\n\t\t\t\t\t\tconsole.log('支付成功', res1);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.getMoney()\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t// 支付失败回调\r\n\t\t\t\t\t\tconsole.error('requestPayment fail object:', err);\r\n\t\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\r\n\t\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '您已取消支付',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconsole.error('支付失败', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付失败请检查网络',\r\n\t\t\t\t\t\t\ticon: 'error'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\tgetMoney(){\r\n\t\t\t\tthis.$api.service.seeBzj().then(res=>{\r\n\t\t\t\t\tthis.money = res.money\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmit(){\r\n\t\t\t\tif(this.money == 0 || this.money == ''){\r\n\t\t\t\t\t// 获取当前平台\r\n\t\t\t\t\tconst platform = this.getCurrentPlatform();\r\n\t\t\t\t\tconsole.log('当前平台:', platform);\r\n\r\n\t\t\t\t\tthis.$api.service.masterPayY().then(res=>{\r\n\t\t\t\t\t\tlet obj = res.pay_list\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\r\n\t\t\t\t\t\tconsole.log(String(packageStr))\r\n\t\t\t\t\t\tconsole.log(obj.nonceStr)\r\n\t\t\t\t\t\tconsole.log(packageStr)\r\n\t\t\t\t\t\tconsole.log(obj.nonceStr)\r\n\t\t\t\t\t\tconsole.log(String(obj.timestamp))\r\n\t\t\t\t\t\tconsole.log(obj.sign)\r\n\r\n\t\t\t\t\t\t// 根据平台选择不同的支付方式\r\n\t\t\t\t\t\tif (platform === 'app-plus') {\r\n\t\t\t\t\t\t\t// APP环境使用微信支付\r\n\t\t\t\t\t\t\tconsole.log('APP环境，使用微信支付');\r\n\t\t\t\t\t\t\tthis.handleAppWechatPay(obj);\r\n\t\t\t\t\t\t} else if (platform === 'mp-weixin') {\r\n\t\t\t\t\t\t\t// 微信小程序环境保持原有逻辑\r\n\t\t\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\r\n\t\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 其他环境（H5等）\r\n\t\t\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\r\n\t\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\ttitle:'您已缴纳保证金快去接单吧'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getMoney()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page{\r\n\tbackground: #F8F8F8;\r\n\theight: 100vh;\r\n\t.box{\r\n\t\tpadding: 50rpx 82rpx;\r\n\t\tbackground: #fff;\r\n\t\t.money{\r\n\t\t\tmargin: 0 auto;\r\n\t\t\twidth: fit-content;\r\n\t\t\tfont-size: 80rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #171717;\r\n\t\t}\r\n\t\t.title{\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\twidth: fit-content;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #171717;\r\n\t\t}\r\n\t\t.btn{\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tmargin-top: 64rpx;\r\n\t\t\twidth: 584rpx;\r\n\t\t\theight: 98rpx;\r\n\t\t\tbackground: #2E80FE;\r\n\t\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\r\n\t\t\tline-height: 98rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=4952e6f2&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=4952e6f2&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754791178616\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}