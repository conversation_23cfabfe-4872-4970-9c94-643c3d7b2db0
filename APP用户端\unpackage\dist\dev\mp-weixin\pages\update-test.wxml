<view class="test-page data-v-41386fa6"><view class="header data-v-41386fa6"><text class="title data-v-41386fa6">热更新功能测试</text></view><view class="test-section data-v-41386fa6"><view class="section-title data-v-41386fa6">当前版本信息</view><view class="info-item data-v-41386fa6"><text class="label data-v-41386fa6">版本号:</text><text class="value data-v-41386fa6">{{currentVersion}}</text></view><view class="info-item data-v-41386fa6"><text class="label data-v-41386fa6">平台类型:</text><text class="value data-v-41386fa6">{{platform===2?'用户端':'师傅端'}}</text></view></view><view class="test-section data-v-41386fa6"><view class="section-title data-v-41386fa6">测试功能</view><button class="test-btn data-v-41386fa6" disabled="{{isChecking}}" data-event-opts="{{[['tap',[['testCheckUpdate',['$event']]]]]}}" bindtap="__e">{{''+(isChecking?'检查中...':'测试版本检查')+''}}</button><button class="test-btn data-v-41386fa6" disabled="{{isChecking}}" data-event-opts="{{[['tap',[['testSilentCheck',['$event']]]]]}}" bindtap="__e">{{''+(isChecking?'检查中...':'测试静默检查')+''}}</button><button data-event-opts="{{[['tap',[['testForceUpdate',['$event']]]]]}}" class="test-btn data-v-41386fa6" bindtap="__e">模拟强制更新</button><button data-event-opts="{{[['tap',[['testOptionalUpdate',['$event']]]]]}}" class="test-btn data-v-41386fa6" bindtap="__e">模拟可选更新</button><button data-event-opts="{{[['tap',[['navigateToUpdatePage',['$event']]]]]}}" class="test-btn data-v-41386fa6" bindtap="__e">打开更新页面</button></view><view class="test-section data-v-41386fa6"><view class="section-title data-v-41386fa6">测试日志</view><scroll-view class="log-container data-v-41386fa6" scroll-y="{{true}}"><block wx:for="{{logs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><view class="log-item data-v-41386fa6"><text class="log-time data-v-41386fa6">{{log.time}}</text><text class="log-content data-v-41386fa6">{{log.content}}</text></view></block></scroll-view><button data-event-opts="{{[['tap',[['clearLogs',['$event']]]]]}}" class="clear-btn data-v-41386fa6" bindtap="__e">清空日志</button></view></view>