<view class="app-update-page data-v-05362ea4"><view class="navbar data-v-05362ea4"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-05362ea4" bindtap="__e"><text class="nav-icon data-v-05362ea4">‹</text></view><view class="nav-title data-v-05362ea4">应用更新</view><view class="nav-right data-v-05362ea4"></view></view><view class="version-info data-v-05362ea4"><view class="app-icon data-v-05362ea4"><image src="/static/images/logo.png" mode="aspectFit" class="data-v-05362ea4"></image></view><view class="version-details data-v-05362ea4"><text class="app-name data-v-05362ea4">今师傅</text><text class="version-text data-v-05362ea4">{{"当前版本: v"+currentVersion}}</text></view></view><view class="check-update-section data-v-05362ea4"><button class="{{['check-btn','data-v-05362ea4',(isChecking)?'checking':'']}}" disabled="{{isChecking}}" data-event-opts="{{[['tap',[['checkUpdate',['$event']]]]]}}" bindtap="__e">{{''+(isChecking?'检查中...':'检查更新')+''}}</button><button data-event-opts="{{[['tap',[['goToTestPage',['$event']]]]]}}" class="test-btn data-v-05362ea4" bindtap="__e">测试页面</button></view><block wx:if="{{updateInfo}}"><view class="update-info data-v-05362ea4"><view class="info-header data-v-05362ea4"><text class="info-title data-v-05362ea4">{{"发现新版本 v"+updateInfo.latestVersion}}</text><block wx:if="{{updateInfo.forceUpdate}}"><view class="update-badge data-v-05362ea4">强制更新</view></block></view><view class="info-content data-v-05362ea4"><text class="info-desc data-v-05362ea4">{{updateInfo.description}}</text></view><view class="info-actions data-v-05362ea4"><button class="update-btn primary data-v-05362ea4" disabled="{{isUpdating}}" data-event-opts="{{[['tap',[['startUpdate',['$event']]]]]}}" bindtap="__e">{{''+(isUpdating?'更新中...':'立即更新')+''}}</button><block wx:if="{{!updateInfo.forceUpdate&&!isUpdating}}"><button data-event-opts="{{[['tap',[['laterUpdate',['$event']]]]]}}" class="update-btn secondary data-v-05362ea4" bindtap="__e">稍后更新</button></block></view></view></block><block wx:if="{{showNoUpdate}}"><view class="no-update data-v-05362ea4"><text class="no-update-text data-v-05362ea4">🎉 已是最新版本</text></view></block><view class="update-notes data-v-05362ea4"><view class="notes-title data-v-05362ea4">更新说明</view><view class="notes-content data-v-05362ea4"><text class="notes-item data-v-05362ea4">• 支持热更新功能，及时获取最新版本</text><text class="notes-item data-v-05362ea4">• 优化用户体验，提升应用性能</text><text class="notes-item data-v-05362ea4">• 修复已知问题，增强应用稳定性</text><text class="notes-item data-v-05362ea4">• 建议及时更新以获得最佳使用体验</text></view></view></view>