@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-9f90fdfa {
  padding: 0 30rpx;
}
.page .form_item.data-v-9f90fdfa {
  height: 120rpx;
  border-bottom: 2rpx solid #E9E9E9;
  display: flex;
  align-items: center;
}
.page .form_item .left.data-v-9f90fdfa {
  width: 190rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .form_item .left ._span.data-v-9f90fdfa {
  color: red;
}
.page .notes.data-v-9f90fdfa {
  padding-top: 42rpx;
}
.page .notes .left.data-v-9f90fdfa {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .notes textarea.data-v-9f90fdfa {
  box-sizing: border-box;
  padding: 40rpx 30rpx;
  margin-top: 40rpx;
  width: 686rpx;
  height: 242rpx;
  background: #F7F7F7;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}
.page .footer.data-v-9f90fdfa {
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  height: 192rpx;
  width: 750rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page .footer .btn.data-v-9f90fdfa {
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}

